USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 30 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 28 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 28 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 28 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 28 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 28 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 30 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 31 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 28 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A005 ***************


Checking for errors during installation

Executing : bash -c grep -i -P 'error[:\s]' /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.

Ignoring log : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*

Ignoring log : /export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists

Ignoring log : /export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:

Ignoring log : /export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message

Ignoring log : /export/home/<USER>/install_platform.log:tr: write error: Broken pipe

Ignoring log : /export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

Checking if every component is successfully installed

Checking if Platform Installed Successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Checking if Third Party package(s) installed successfully
Executing command : bash -c grep -i "Third Party package(s) installed successfully" /export/home/<USER>/install_third_party.log
Std Input Stream : Third Party package(s) installed successfully
Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Checking if Java Installed Successfully
Executing command : bash -c grep -i "Java Installed Successfully" /export/home/<USER>/install_java.log
Std Input Stream : Java Installed Successfully
Java Installed Successfully - Found in /export/home/<USER>/install_java.log

Checking if pdmPackage was installed successfully
Executing command : bash -c grep -i "pdmPackage was installed successfully" /export/home/<USER>/install_pdm.log
Std Input Stream : pdmPackage was installed successfully
pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

Checking if CSE Installed Successfully
Executing command : bash -c grep -i "CSE Installed Successfully" /export/home/<USER>/install_cse.log
Std Input Stream : CSE Installed Successfully
CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i -P 'error[:\s]' /export/home/<USER>/*.log
InputCheckInstallationSuccess = Platform Installed Successfully:/export/home/<USER>/install_platform.log,Third Party package(s) installed successfully:/export/home/<USER>/install_third_party.log,Java Installed Successfully:/export/home/<USER>/install_java.log,pdmPackage was installed successfully:/export/home/<USER>/install_pdm.log,CSE Installed Successfully:/export/home/<USER>/install_cse.log
OutputIgnoreErrorLogs = /export/home/<USER>/edoc_install.log,/export/home/<USER>/iCoreStartUp.log,/export/home/<USER>/iCoreSetup.log,/export/home/<USER>/swapps_install.log,/export/home/<USER>/install_cse.log,/export/home/<USER>/install_platform.log,/export/home/<USER>/InSiteAgentInstall.log,/export/home/<USER>/install_python3.log,Error: no DISPLAY environment variable specified,ERROR: The usb feature may not work since required packages are not present in the system.
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:error: File not found by glob: nuevo-2p*
/export/home/<USER>/install_cse.log:Calling Aux Schema with database dbexpress Database1 :psql:/export/home/<USER>/nuevo/resources/dbx/schema/AuxillarySchema.sql:1: ERROR:  database "dbexpress" already exists
/export/home/<USER>/install_cse.log:psql: error: connection to server on socket "/run/postgresql/.s.PGSQL.5432" failed: FATAL:  role "root" does not exist
/export/home/<USER>/install_cse.log:INFO: Adding lines not to throw tty error when running with sudo
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:INFO: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_cse.log:WARNING: Error encountered when attempting EAT log: Server not running:
/export/home/<USER>/install_platform.log:INFO  [alembic.runtime.migration] Running upgrade fe681d9cf1ab -> f415ec77f861, Move return_data to tasks, Change task_events.error to task_events.message
/export/home/<USER>/install_platform.log:tr: write error: Broken pipe
/export/home/<USER>/install_platform.log:WARNING: There was an error checking the latest version of pip.

No unexpected errors found in RPMs installation

Platform Installed Successfully - Found in /export/home/<USER>/install_platform.log

Third Party package(s) installed successfully - Found in /export/home/<USER>/install_third_party.log

Java Installed Successfully - Found in /export/home/<USER>/install_java.log

pdmPackage was installed successfully - Found in /export/home/<USER>/install_pdm.log

CSE Installed Successfully - Found in /export/home/<USER>/install_cse.log
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A005 ***************

Test Result : SUCCESS
