
**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_Browser
<PERSON><PERSON><PERSON> Name - VERIFY_SIGMOID_TEST_PATTERN_PRESENT
TestCase Start Time - 01-07-2027 07:34:12.340 AM
**********************************************************************************

2027-07-01T07:34:12	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30190035
AUTID: 30190037
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190037
RUNNERID: 30190038
AUTID: 30190039
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190039
RUNNERID: 30190040
AUTID: 30190041
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190041
RUNNERID: 30190042
AUTID: 30190043
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190043
2027-07-01T07:34:12	START_TEST_CASE	Start 'tst_Browser'           	Test 'tst_Browser' started (tst_Browser)
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: Start Feature	Browser
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: Start Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:83: Start Example	PatientName=Sigmoid Test Pattern
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: Start Step	Given PDM is UP
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T07:34:18	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: End Step	Given PDM is UP
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: Start Step	When Clicked on Browser tab
2027-07-01T07:34:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T07:34:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T07:34:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T07:34:25	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T07:34:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T07:34:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T07:34:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T07:34:25	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T07:34:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: End Step	When Clicked on Browser tab
2027-07-01T07:34:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: Start Step	And Browser is Up and Running
2027-07-01T07:34:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T07:34:25	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T07:34:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: End Step	And Browser is Up and Running
2027-07-01T07:34:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: Start Step	And Select or deselect column from column chooser
2027-07-01T07:34:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step :- Select or deselect column from column chooser	
2027-07-01T07:34:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T07:34:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient name column chooser is found	
2027-07-01T07:34:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient name column chooser is Selected	
2027-07-01T07:34:26	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient name column chooser Checkbox is checked	
2027-07-01T07:34:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
RUNNERID: 30190044
AUTID: 30190045
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190045
2027-07-01T07:34:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T07:34:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T07:34:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object ExamDescription Column Chooser is found	
2027-07-01T07:34:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field ExamDescription Column Chooser is Selected	
2027-07-01T07:34:27	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :ExamDescription Column Chooser Checkbox is checked	
2027-07-01T07:34:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T07:34:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T07:34:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T07:34:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient Id column chooser is found	
2027-07-01T07:34:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient Id column chooser is Selected	
2027-07-01T07:34:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient Id column chooser Checkbox is checked	
2027-07-01T07:34:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T07:34:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T07:34:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T07:34:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Networked Column Chooser is found	
2027-07-01T07:34:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Networked Column Chooser is Selected	
2027-07-01T07:34:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Networked Column Chooser Checkbox is checked	
2027-07-01T07:34:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T07:34:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T07:34:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Archieved Column chooser is found	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Archieved Column chooser is Selected	
2027-07-01T07:34:30	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Archieved Column chooser Checkbox is checked	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam id column chooser is found	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam id column chooser is Selected	
2027-07-01T07:34:30	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam id column chooser Checkbox is checked	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: End Step	And Select or deselect column from column chooser
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: Start Step	Given PDM is UP
2027-07-01T07:34:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T07:34:31	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T07:34:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: End Step	Given PDM is UP
2027-07-01T07:34:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: Start Step	When Clicked on Browser tab
RUNNERID: 30190046
AUTID: 30190047
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190047
RUNNERID: 30190048
AUTID: 30190049
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190049
RUNNERID: 30190050
AUTID: 30190051
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190051
2027-07-01T07:34:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T07:34:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T07:34:34	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T07:34:37	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T07:34:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T07:34:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T07:34:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T07:34:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T07:34:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: End Step	When Clicked on Browser tab
2027-07-01T07:34:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: Start Step	When Browser is Up and Running
2027-07-01T07:34:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T07:34:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T07:34:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: End Step	When Browser is Up and Running
2027-07-01T07:34:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: Start Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T07:34:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select an Exam '<PatientName>' from Browser	
2027-07-01T07:34:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Browser Exam search field is performed	
2027-07-01T07:34:39	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Browser study table Search box selected successfully	
2027-07-01T07:34:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T07:34:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T07:34:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser Exam search field is Editable	
2027-07-01T07:34:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Browser Exam search field is performed	
2027-07-01T07:34:39	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Value entered in the search box successfully	
2027-07-01T07:34:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click action is performed on option menu 0/1	
2027-07-01T07:34:44	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Patient in the first row of the Browser study table selected successfully	
2027-07-01T07:34:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: End Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T07:34:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: Start Step	Then I Click on 'X' in the Image Management search area
2027-07-01T07:34:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I Click on 'X' in the Image Management search area	
2027-07-01T07:34:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T07:34:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Exam search field is performed	
2027-07-01T07:34:48	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Performed mouseClick on Browser Exam search field	
2027-07-01T07:34:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: End Step	Then I Click on 'X' in the Image Management search area
2027-07-01T07:34:48	LOG       	End Example                   	
2027-07-01T07:34:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: End Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T07:34:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: End Feature	Browser
2027-07-01T07:34:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T07:34:48	END_TEST_CASE	End 'tst_Browser'             	End of test 'tst_Browser'
2027-07-01T07:34:49	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	  20
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	  20
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 07:34:49.261 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_Browser
Scenario Name - VERIFY_SIGMOID_TEST_PATTERN_PRESENT
TestCase Start Time - 01-07-2027 10:04:55.472 AM
**********************************************************************************

2027-07-01T10:04:55	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 28010035
AUTID: 28010037
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010037
RUNNERID: 28010038
AUTID: 28010039
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010039
RUNNERID: 28010040
AUTID: 28010041
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010041
RUNNERID: 28010042
AUTID: 28010043
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010043
2027-07-01T10:04:55	START_TEST_CASE	Start 'tst_Browser'           	Test 'tst_Browser' started (tst_Browser)
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: Start Feature	Browser
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: Start Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:83: Start Example	PatientName=Sigmoid Test Pattern
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: Start Step	Given PDM is UP
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:05:01	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: End Step	Given PDM is UP
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: Start Step	When Clicked on Browser tab
2027-07-01T10:05:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T10:05:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T10:05:05	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T10:05:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T10:05:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T10:05:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T10:05:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T10:05:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:05:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: End Step	When Clicked on Browser tab
2027-07-01T10:05:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: Start Step	And Browser is Up and Running
2027-07-01T10:05:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T10:05:09	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T10:05:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: End Step	And Browser is Up and Running
2027-07-01T10:05:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: Start Step	And Select or deselect column from column chooser
2027-07-01T10:05:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step :- Select or deselect column from column chooser	
2027-07-01T10:05:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T10:05:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient name column chooser is found	
2027-07-01T10:05:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient name column chooser is Selected	
2027-07-01T10:05:09	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient name column chooser Checkbox is checked	
2027-07-01T10:05:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
RUNNERID: 28010044
AUTID: 28010045
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010045
2027-07-01T10:05:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T10:05:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T10:05:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object ExamDescription Column Chooser is found	
2027-07-01T10:05:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field ExamDescription Column Chooser is Selected	
2027-07-01T10:05:10	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :ExamDescription Column Chooser Checkbox is checked	
2027-07-01T10:05:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T10:05:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T10:05:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T10:05:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient Id column chooser is found	
2027-07-01T10:05:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient Id column chooser is Selected	
2027-07-01T10:05:11	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient Id column chooser Checkbox is checked	
2027-07-01T10:05:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T10:05:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T10:05:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T10:05:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Networked Column Chooser is found	
2027-07-01T10:05:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Networked Column Chooser is Selected	
2027-07-01T10:05:12	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Networked Column Chooser Checkbox is checked	
2027-07-01T10:05:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T10:05:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T10:05:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T10:05:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Archieved Column chooser is found	
2027-07-01T10:05:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Archieved Column chooser is Selected	
2027-07-01T10:05:13	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Archieved Column chooser Checkbox is checked	
2027-07-01T10:05:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T10:05:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam id column chooser is found	
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam id column chooser is Selected	
2027-07-01T10:05:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam id column chooser Checkbox is checked	
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: End Step	And Select or deselect column from column chooser
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: Start Step	Given PDM is UP
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:05:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: End Step	Given PDM is UP
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: Start Step	When Clicked on Browser tab
RUNNERID: 28010046
AUTID: 28010047
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010047
RUNNERID: 28010048
AUTID: 28010049
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010049
RUNNERID: 28010050
AUTID: 28010051
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010051
2027-07-01T10:05:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T10:05:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T10:05:17	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T10:05:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T10:05:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T10:05:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T10:05:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T10:05:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:05:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: End Step	When Clicked on Browser tab
2027-07-01T10:05:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: Start Step	When Browser is Up and Running
2027-07-01T10:05:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T10:05:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T10:05:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: End Step	When Browser is Up and Running
2027-07-01T10:05:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: Start Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T10:05:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select an Exam '<PatientName>' from Browser	
2027-07-01T10:05:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Browser Exam search field is performed	
2027-07-01T10:05:22	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Browser study table Search box selected successfully	
2027-07-01T10:05:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T10:05:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T10:05:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser Exam search field is Editable	
2027-07-01T10:05:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Browser Exam search field is performed	
2027-07-01T10:05:23	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Value entered in the search box successfully	
2027-07-01T10:05:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click action is performed on option menu 0/1	
2027-07-01T10:05:27	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Patient in the first row of the Browser study table selected successfully	
2027-07-01T10:05:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: End Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T10:05:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: Start Step	Then I Click on 'X' in the Image Management search area
2027-07-01T10:05:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I Click on 'X' in the Image Management search area	
2027-07-01T10:05:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T10:05:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Exam search field is performed	
2027-07-01T10:05:32	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Performed mouseClick on Browser Exam search field	
2027-07-01T10:05:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: End Step	Then I Click on 'X' in the Image Management search area
2027-07-01T10:05:32	LOG       	End Example                   	
2027-07-01T10:05:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: End Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T10:05:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: End Feature	Browser
2027-07-01T10:05:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:05:32	END_TEST_CASE	End 'tst_Browser'             	End of test 'tst_Browser'
2027-07-01T10:05:33	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	  20
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	  20
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:05:33.048 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_Browser
Scenario Name - VERIFY_SIGMOID_TEST_PATTERN_PRESENT
TestCase Start Time - 01-07-2027 10:45:20.119 AM
**********************************************************************************

2027-07-01T10:45:20	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 29800027
AUTID: 29800029
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29800029
RUNNERID: 29800030
AUTID: 29800031
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29800031
RUNNERID: 29800032
AUTID: 29800033
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29800033
RUNNERID: 29800034
AUTID: 29800035
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29800035
2027-07-01T10:45:20	START_TEST_CASE	Start 'tst_Browser'           	Test 'tst_Browser' started (tst_Browser)
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: Start Feature	Browser
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: Start Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:83: Start Example	PatientName=Sigmoid Test Pattern
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: Start Step	Given PDM is UP
2027-07-01T10:45:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:45:26	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:45:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: End Step	Given PDM is UP
2027-07-01T10:45:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: Start Step	When Clicked on Browser tab
2027-07-01T10:45:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T10:45:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T10:45:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T10:45:32	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T10:45:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T10:45:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T10:45:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T10:45:33	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:45:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: End Step	When Clicked on Browser tab
2027-07-01T10:45:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: Start Step	And Browser is Up and Running
2027-07-01T10:45:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T10:45:33	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T10:45:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: End Step	And Browser is Up and Running
2027-07-01T10:45:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: Start Step	And Select or deselect column from column chooser
2027-07-01T10:45:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step :- Select or deselect column from column chooser	
2027-07-01T10:45:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T10:45:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient name column chooser is Not Selected	
2027-07-01T10:45:36	WARNING   	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:35: ERROR :Mouse click object Study table Patient name column chooser could not be found	
2027-07-01T10:45:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_10:45:36.PNG	
2027-07-01T10:45:37	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:58: Script Error	RuntimeError: TEST FAILURE :Failed to check Study table Patient name column chooser checkbox:
2027-07-01T10:45:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: End Step	And Select or deselect column from column chooser
2027-07-01T10:45:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: Skip Step	Given PDM is UP
2027-07-01T10:45:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: Skip Step	When Clicked on Browser tab
2027-07-01T10:45:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: Skip Step	When Browser is Up and Running
2027-07-01T10:45:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: Skip Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T10:45:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: Skip Step	Then I Click on 'X' in the Image Management search area
2027-07-01T10:45:37	LOG       	End Example                   	
2027-07-01T10:45:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: End Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T10:45:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: End Feature	Browser
2027-07-01T10:45:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:45:37	END_TEST_CASE	End 'tst_Browser'             	End of test 'tst_Browser'
2027-07-01T10:45:37	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   1
*******************************************************
ERROR:root:Mouse click object Study table Patient name column chooser could not be found

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:45:37.557 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_Browser
Scenario Name - VERIFY_SIGMOID_TEST_PATTERN_PRESENT
TestCase Start Time - 01-07-2027 11:05:53.767 AM
**********************************************************************************

2027-07-01T11:05:53	START     	Start 'TestCases'             	Test 'TestCases' started
2027-07-01T11:05:53	START_TEST_CASE	Start 'tst_Browser'           	Test 'tst_Browser' started (tst_Browser)
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: Start Feature	Browser
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: Start Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:83: Start Example	PatientName=Sigmoid Test Pattern
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: Start Step	Given PDM is UP
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_11:05:57.PNG	
2027-07-01T11:05:57	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Squish_Wrapper/Wrapper.py:713: Script Error	RuntimeError: Screenshot taken from the desktop is invalid (Perhaps the AUT has quit/crashed unexpectedly?)
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: End Step	Given PDM is UP
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: Skip Step	When Clicked on Browser tab
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: Skip Step	And Browser is Up and Running
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: Skip Step	And Select or deselect column from column chooser
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: Skip Step	Given PDM is UP
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: Skip Step	When Clicked on Browser tab
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: Skip Step	When Browser is Up and Running
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: Skip Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: Skip Step	Then I Click on 'X' in the Image Management search area
2027-07-01T11:05:57	LOG       	End Example                   	
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: End Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: End Feature	Browser
2027-07-01T11:05:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:05:57	END_TEST_CASE	End 'tst_Browser'             	End of test 'tst_Browser'
2027-07-01T11:05:58	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   0
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   0
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:05:58.443 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_Browser
Scenario Name - VERIFY_SIGMOID_TEST_PATTERN_PRESENT
TestCase Start Time - 01-07-2027 11:49:47.284 AM
**********************************************************************************

2027-07-01T11:49:47	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30460063
AUTID: 30460065
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460065
RUNNERID: 30460066
AUTID: 30460067
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460067
RUNNERID: 30460068
AUTID: 30460069
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460069
RUNNERID: 30460070
AUTID: 30460071
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460071
2027-07-01T11:49:47	START_TEST_CASE	Start 'tst_Browser'           	Test 'tst_Browser' started (tst_Browser)
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: Start Feature	Browser
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: Start Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:83: Start Example	PatientName=Sigmoid Test Pattern
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: Start Step	Given PDM is UP
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:49:53	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: End Step	Given PDM is UP
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: Start Step	When Clicked on Browser tab
2027-07-01T11:49:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T11:49:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T11:49:56	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T11:50:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T11:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T11:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T11:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T11:50:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: End Step	When Clicked on Browser tab
2027-07-01T11:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: Start Step	And Browser is Up and Running
2027-07-01T11:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T11:50:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T11:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: End Step	And Browser is Up and Running
2027-07-01T11:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: Start Step	And Select or deselect column from column chooser
2027-07-01T11:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step :- Select or deselect column from column chooser	
2027-07-01T11:50:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T11:50:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient name column chooser is found	
2027-07-01T11:50:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient name column chooser is Selected	
2027-07-01T11:50:01	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient name column chooser Checkbox is checked	
2027-07-01T11:50:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
RUNNERID: 30460072
AUTID: 30460073
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460073
2027-07-01T11:50:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T11:50:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T11:50:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object ExamDescription Column Chooser is found	
2027-07-01T11:50:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field ExamDescription Column Chooser is Selected	
2027-07-01T11:50:02	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :ExamDescription Column Chooser Checkbox is checked	
2027-07-01T11:50:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T11:50:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T11:50:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T11:50:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient Id column chooser is found	
2027-07-01T11:50:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient Id column chooser is Selected	
2027-07-01T11:50:03	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient Id column chooser Checkbox is checked	
2027-07-01T11:50:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T11:50:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T11:50:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T11:50:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Networked Column Chooser is found	
2027-07-01T11:50:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Networked Column Chooser is Selected	
2027-07-01T11:50:04	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Networked Column Chooser Checkbox is checked	
2027-07-01T11:50:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T11:50:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T11:50:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T11:50:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Archieved Column chooser is found	
2027-07-01T11:50:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Archieved Column chooser is Selected	
2027-07-01T11:50:04	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Archieved Column chooser Checkbox is checked	
2027-07-01T11:50:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T11:50:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T11:50:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T11:50:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam id column chooser is found	
2027-07-01T11:50:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam id column chooser is Selected	
2027-07-01T11:50:05	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam id column chooser Checkbox is checked	
2027-07-01T11:50:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T11:50:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T11:50:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: End Step	And Select or deselect column from column chooser
2027-07-01T11:50:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: Start Step	Given PDM is UP
2027-07-01T11:50:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:50:06	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:50:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: End Step	Given PDM is UP
2027-07-01T11:50:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: Start Step	When Clicked on Browser tab
RUNNERID: 30460074
AUTID: 30460075
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460075
RUNNERID: 30460076
AUTID: 30460077
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460077
RUNNERID: 30460078
AUTID: 30460079
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460079
2027-07-01T11:50:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T11:50:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T11:50:09	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T11:50:12	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T11:50:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T11:50:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T11:50:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T11:50:13	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:50:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: End Step	When Clicked on Browser tab
2027-07-01T11:50:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: Start Step	When Browser is Up and Running
2027-07-01T11:50:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T11:50:13	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T11:50:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: End Step	When Browser is Up and Running
2027-07-01T11:50:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: Start Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T11:50:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select an Exam '<PatientName>' from Browser	
2027-07-01T11:50:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Browser Exam search field is performed	
2027-07-01T11:50:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Browser study table Search box selected successfully	
2027-07-01T11:50:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T11:50:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T11:50:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser Exam search field is Editable	
2027-07-01T11:50:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Browser Exam search field is performed	
2027-07-01T11:50:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Value entered in the search box successfully	
2027-07-01T11:50:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click action is performed on option menu 0/1	
2027-07-01T11:50:18	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Patient in the first row of the Browser study table selected successfully	
2027-07-01T11:50:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: End Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T11:50:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: Start Step	Then I Click on 'X' in the Image Management search area
2027-07-01T11:50:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I Click on 'X' in the Image Management search area	
2027-07-01T11:50:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T11:50:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Exam search field is performed	
2027-07-01T11:50:23	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Performed mouseClick on Browser Exam search field	
2027-07-01T11:50:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: End Step	Then I Click on 'X' in the Image Management search area
2027-07-01T11:50:23	LOG       	End Example                   	
2027-07-01T11:50:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: End Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T11:50:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: End Feature	Browser
2027-07-01T11:50:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:50:23	END_TEST_CASE	End 'tst_Browser'             	End of test 'tst_Browser'
2027-07-01T11:50:24	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	  20
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	  20
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:50:24.031 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_Browser
Scenario Name - VERIFY_SIGMOID_TEST_PATTERN_PRESENT
TestCase Start Time - 01-07-2027 12:22:02.099 PM
**********************************************************************************

2027-07-01T12:22:02	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130063
AUTID: 30130065
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130065
RUNNERID: 30130066
AUTID: 30130067
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130067
RUNNERID: 30130068
AUTID: 30130069
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130069
RUNNERID: 30130070
AUTID: 30130071
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130071
2027-07-01T12:22:02	START_TEST_CASE	Start 'tst_Browser'           	Test 'tst_Browser' started (tst_Browser)
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: Start Feature	Browser
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: Start Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:83: Start Example	PatientName=Sigmoid Test Pattern
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: Start Step	Given PDM is UP
2027-07-01T12:22:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:22:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:22:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: End Step	Given PDM is UP
2027-07-01T12:22:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: Start Step	When Clicked on Browser tab
2027-07-01T12:22:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T12:22:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T12:22:11	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T12:22:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T12:22:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T12:22:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T12:22:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T12:22:15	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:22:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: End Step	When Clicked on Browser tab
2027-07-01T12:22:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: Start Step	And Browser is Up and Running
2027-07-01T12:22:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T12:22:15	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T12:22:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: End Step	And Browser is Up and Running
2027-07-01T12:22:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: Start Step	And Select or deselect column from column chooser
2027-07-01T12:22:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step :- Select or deselect column from column chooser	
2027-07-01T12:22:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T12:22:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient name column chooser is found	
2027-07-01T12:22:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient name column chooser is Selected	
2027-07-01T12:22:16	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient name column chooser Checkbox is checked	
2027-07-01T12:22:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
RUNNERID: 30130072
AUTID: 30130073
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130073
2027-07-01T12:22:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T12:22:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T12:22:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object ExamDescription Column Chooser is found	
2027-07-01T12:22:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field ExamDescription Column Chooser is Selected	
2027-07-01T12:22:17	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :ExamDescription Column Chooser Checkbox is checked	
2027-07-01T12:22:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T12:22:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient Id column chooser is found	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient Id column chooser is Selected	
2027-07-01T12:22:18	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient Id column chooser Checkbox is checked	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Networked Column Chooser is found	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Networked Column Chooser is Selected	
2027-07-01T12:22:18	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Networked Column Chooser Checkbox is checked	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T12:22:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T12:22:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T12:22:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Archieved Column chooser is found	
2027-07-01T12:22:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Archieved Column chooser is Selected	
2027-07-01T12:22:19	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Archieved Column chooser Checkbox is checked	
2027-07-01T12:22:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T12:22:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T12:22:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T12:22:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam id column chooser is found	
2027-07-01T12:22:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam id column chooser is Selected	
2027-07-01T12:22:20	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam id column chooser Checkbox is checked	
2027-07-01T12:22:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T12:22:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T12:22:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: End Step	And Select or deselect column from column chooser
2027-07-01T12:22:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: Start Step	Given PDM is UP
2027-07-01T12:22:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:22:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:22:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: End Step	Given PDM is UP
2027-07-01T12:22:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: Start Step	When Clicked on Browser tab
RUNNERID: 30130074
AUTID: 30130075
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130075
RUNNERID: 30130076
AUTID: 30130077
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130077
RUNNERID: 30130078
AUTID: 30130079
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130079
2027-07-01T12:22:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T12:22:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T12:22:24	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T12:22:27	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T12:22:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T12:22:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T12:22:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T12:22:27	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:22:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: End Step	When Clicked on Browser tab
2027-07-01T12:22:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: Start Step	When Browser is Up and Running
2027-07-01T12:22:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T12:22:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T12:22:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: End Step	When Browser is Up and Running
2027-07-01T12:22:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: Start Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T12:22:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select an Exam '<PatientName>' from Browser	
2027-07-01T12:22:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Browser Exam search field is performed	
2027-07-01T12:22:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Browser study table Search box selected successfully	
2027-07-01T12:22:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T12:22:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T12:22:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser Exam search field is Editable	
2027-07-01T12:22:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Browser Exam search field is performed	
2027-07-01T12:22:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Value entered in the search box successfully	
2027-07-01T12:22:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click action is performed on option menu 0/1	
2027-07-01T12:22:33	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Patient in the first row of the Browser study table selected successfully	
2027-07-01T12:22:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: End Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T12:22:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: Start Step	Then I Click on 'X' in the Image Management search area
2027-07-01T12:22:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I Click on 'X' in the Image Management search area	
2027-07-01T12:22:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T12:22:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Exam search field is performed	
2027-07-01T12:22:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Performed mouseClick on Browser Exam search field	
2027-07-01T12:22:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: End Step	Then I Click on 'X' in the Image Management search area
2027-07-01T12:22:38	LOG       	End Example                   	
2027-07-01T12:22:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: End Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T12:22:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: End Feature	Browser
2027-07-01T12:22:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:22:38	END_TEST_CASE	End 'tst_Browser'             	End of test 'tst_Browser'
2027-07-01T12:22:38	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	  20
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	  20
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:22:38.939 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_Browser
Scenario Name - VERIFY_SIGMOID_TEST_PATTERN_PRESENT
TestCase Start Time - 01-07-2027 12:57:50.703 PM
**********************************************************************************

2027-07-01T12:57:50	START     	Start 'TestCases'             	Test 'TestCases' started
2027-07-01T12:57:50	START_TEST_CASE	Start 'tst_Browser'           	Test 'tst_Browser' started (tst_Browser)
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: Start Feature	Browser
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: Start Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:83: Start Example	PatientName=Sigmoid Test Pattern
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: Start Step	Given PDM is UP
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_12:57:55.PNG	
2027-07-01T12:57:55	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Squish_Wrapper/Wrapper.py:713: Script Error	RuntimeError: Screenshot taken from the desktop is invalid (Perhaps the AUT has quit/crashed unexpectedly?)
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: End Step	Given PDM is UP
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: Skip Step	When Clicked on Browser tab
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: Skip Step	And Browser is Up and Running
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: Skip Step	And Select or deselect column from column chooser
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: Skip Step	Given PDM is UP
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: Skip Step	When Clicked on Browser tab
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: Skip Step	When Browser is Up and Running
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: Skip Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: Skip Step	Then I Click on 'X' in the Image Management search area
2027-07-01T12:57:55	LOG       	End Example                   	
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: End Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: End Feature	Browser
2027-07-01T12:57:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:57:55	END_TEST_CASE	End 'tst_Browser'             	End of test 'tst_Browser'
2027-07-01T12:57:55	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   0
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   0
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:57:55.583 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_Browser
Scenario Name - VERIFY_SIGMOID_TEST_PATTERN_PRESENT
TestCase Start Time - 01-07-2027 13:07:46.805 PM
**********************************************************************************

2027-07-01T13:07:46	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30210063
AUTID: 30210065
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30210065
RUNNERID: 30210066
AUTID: 30210067
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30210067
RUNNERID: 30210068
AUTID: 30210069
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30210069
RUNNERID: 30210070
AUTID: 30210071
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30210071
2027-07-01T13:07:46	START_TEST_CASE	Start 'tst_Browser'           	Test 'tst_Browser' started (tst_Browser)
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: Start Feature	Browser
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: Start Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:83: Start Example	PatientName=Sigmoid Test Pattern
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: Start Step	Given PDM is UP
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T13:07:52	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: End Step	Given PDM is UP
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: Start Step	When Clicked on Browser tab
2027-07-01T13:07:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T13:07:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T13:07:55	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T13:07:59	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T13:07:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T13:07:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T13:07:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T13:07:59	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T13:07:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: End Step	When Clicked on Browser tab
2027-07-01T13:07:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: Start Step	And Browser is Up and Running
2027-07-01T13:07:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T13:08:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T13:08:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: End Step	And Browser is Up and Running
2027-07-01T13:08:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: Start Step	And Select or deselect column from column chooser
2027-07-01T13:08:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step :- Select or deselect column from column chooser	
2027-07-01T13:08:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T13:08:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient name column chooser is found	
2027-07-01T13:08:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient name column chooser is Selected	
2027-07-01T13:08:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient name column chooser Checkbox is checked	
2027-07-01T13:08:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
RUNNERID: 30210072
AUTID: 30210073
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30210073
2027-07-01T13:08:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T13:08:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T13:08:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object ExamDescription Column Chooser is found	
2027-07-01T13:08:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field ExamDescription Column Chooser is Selected	
2027-07-01T13:08:01	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :ExamDescription Column Chooser Checkbox is checked	
2027-07-01T13:08:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T13:08:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T13:08:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T13:08:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient Id column chooser is found	
2027-07-01T13:08:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient Id column chooser is Selected	
2027-07-01T13:08:02	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient Id column chooser Checkbox is checked	
2027-07-01T13:08:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T13:08:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T13:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T13:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Networked Column Chooser is found	
2027-07-01T13:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Networked Column Chooser is Selected	
2027-07-01T13:08:03	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Networked Column Chooser Checkbox is checked	
2027-07-01T13:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T13:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T13:08:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T13:08:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Archieved Column chooser is found	
2027-07-01T13:08:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Archieved Column chooser is Selected	
2027-07-01T13:08:04	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Archieved Column chooser Checkbox is checked	
2027-07-01T13:08:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T13:08:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam id column chooser is found	
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam id column chooser is Selected	
2027-07-01T13:08:05	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam id column chooser Checkbox is checked	
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: End Step	And Select or deselect column from column chooser
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: Start Step	Given PDM is UP
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T13:08:05	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: End Step	Given PDM is UP
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: Start Step	When Clicked on Browser tab
RUNNERID: 30210074
AUTID: 30210075
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30210075
RUNNERID: 30210076
AUTID: 30210077
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30210077
RUNNERID: 30210078
AUTID: 30210079
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30210079
2027-07-01T13:08:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2027-07-01T13:08:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2027-07-01T13:08:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2027-07-01T13:08:12	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T13:08:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2027-07-01T13:08:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2027-07-01T13:08:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2027-07-01T13:08:12	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T13:08:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: End Step	When Clicked on Browser tab
2027-07-01T13:08:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: Start Step	When Browser is Up and Running
2027-07-01T13:08:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2027-07-01T13:08:12	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2027-07-01T13:08:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: End Step	When Browser is Up and Running
2027-07-01T13:08:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: Start Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T13:08:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select an Exam '<PatientName>' from Browser	
2027-07-01T13:08:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Browser Exam search field is performed	
2027-07-01T13:08:13	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Browser study table Search box selected successfully	
2027-07-01T13:08:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T13:08:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T13:08:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser Exam search field is Editable	
2027-07-01T13:08:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Browser Exam search field is performed	
2027-07-01T13:08:13	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Value entered in the search box successfully	
2027-07-01T13:08:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click action is performed on option menu 0/1	
2027-07-01T13:08:18	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Patient in the first row of the Browser study table selected successfully	
2027-07-01T13:08:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: End Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2027-07-01T13:08:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: Start Step	Then I Click on 'X' in the Image Management search area
2027-07-01T13:08:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I Click on 'X' in the Image Management search area	
2027-07-01T13:08:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2027-07-01T13:08:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Exam search field is performed	
2027-07-01T13:08:23	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Performed mouseClick on Browser Exam search field	
2027-07-01T13:08:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: End Step	Then I Click on 'X' in the Image Management search area
2027-07-01T13:08:23	LOG       	End Example                   	
2027-07-01T13:08:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: End Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2027-07-01T13:08:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: End Feature	Browser
2027-07-01T13:08:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T13:08:23	END_TEST_CASE	End 'tst_Browser'             	End of test 'tst_Browser'
2027-07-01T13:08:23	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	  20
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	  20
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 13:08:23.517 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_Browser
Scenario Name - VERIFY_SIGMOID_TEST_PATTERN_PRESENT
TestCase Start Time - 01-07-2025 13:49:33.990 PM
**********************************************************************************

2025-07-01T13:49:34	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130063
AUTID: 30130065
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130065
RUNNERID: 30130066
AUTID: 30130067
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130067
RUNNERID: 30130068
AUTID: 30130069
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130069
RUNNERID: 30130070
AUTID: 30130071
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130071
2025-07-01T13:49:34	START_TEST_CASE	Start 'tst_Browser'           	Test 'tst_Browser' started (tst_Browser)
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: Start Feature	Browser
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: Start Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:83: Start Example	PatientName=Sigmoid Test Pattern
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: Start Step	Given PDM is UP
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:49:39	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:4: End Step	Given PDM is UP
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: Start Step	When Clicked on Browser tab
2025-07-01T13:49:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2025-07-01T13:49:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2025-07-01T13:49:43	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2025-07-01T13:49:46	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2025-07-01T13:49:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2025-07-01T13:49:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2025-07-01T13:49:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2025-07-01T13:49:46	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:49:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:5: End Step	When Clicked on Browser tab
2025-07-01T13:49:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: Start Step	And Browser is Up and Running
2025-07-01T13:49:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2025-07-01T13:49:47	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2025-07-01T13:49:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:6: End Step	And Browser is Up and Running
2025-07-01T13:49:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: Start Step	And Select or deselect column from column chooser
2025-07-01T13:49:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step :- Select or deselect column from column chooser	
2025-07-01T13:49:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2025-07-01T13:49:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient name column chooser is found	
2025-07-01T13:49:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient name column chooser is Selected	
2025-07-01T13:49:48	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient name column chooser Checkbox is checked	
2025-07-01T13:49:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
RUNNERID: 30130072
AUTID: 30130073
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130073
2025-07-01T13:49:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2025-07-01T13:49:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2025-07-01T13:49:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object ExamDescription Column Chooser is found	
2025-07-01T13:49:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field ExamDescription Column Chooser is Selected	
2025-07-01T13:49:48	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :ExamDescription Column Chooser Checkbox is checked	
2025-07-01T13:49:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2025-07-01T13:49:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2025-07-01T13:49:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2025-07-01T13:49:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Study table Patient Id column chooser is found	
2025-07-01T13:49:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Study table Patient Id column chooser is Selected	
2025-07-01T13:49:49	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Study table Patient Id column chooser Checkbox is checked	
2025-07-01T13:49:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2025-07-01T13:49:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2025-07-01T13:49:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2025-07-01T13:49:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Networked Column Chooser is found	
2025-07-01T13:49:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Networked Column Chooser is Selected	
2025-07-01T13:49:50	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Networked Column Chooser Checkbox is checked	
2025-07-01T13:49:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2025-07-01T13:49:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2025-07-01T13:49:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2025-07-01T13:49:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam Archieved Column chooser is found	
2025-07-01T13:49:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam Archieved Column chooser is Selected	
2025-07-01T13:49:51	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam Archieved Column chooser Checkbox is checked	
2025-07-01T13:49:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2025-07-01T13:49:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Study table header is performed	
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Exam id column chooser is found	
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Field Exam id column chooser is Selected	
2025-07-01T13:49:52	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Exam id column chooser Checkbox is checked	
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser study table Column chooser pop up is found	
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser study table Column chooser pop up is Visible	
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:7: End Step	And Select or deselect column from column chooser
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: Start Step	Given PDM is UP
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:49:52	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:76: End Step	Given PDM is UP
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: Start Step	When Clicked on Browser tab
RUNNERID: 30130074
AUTID: 30130075
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130075
RUNNERID: 30130076
AUTID: 30130077
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130077
RUNNERID: 30130078
AUTID: 30130079
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130079
2025-07-01T13:49:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Browser tab	
2025-07-01T13:49:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: tab click object  is performed	
2025-07-01T13:49:55	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Tab click performed on None	
2025-07-01T13:49:59	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2025-07-01T13:49:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser search field is found	
2025-07-01T13:49:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser search field is Visible	
2025-07-01T13:49:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Browser launched successfully	
2025-07-01T13:49:59	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:49:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:77: End Step	When Clicked on Browser tab
2025-07-01T13:49:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: Start Step	When Browser is Up and Running
2025-07-01T13:49:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: browser is Up and Running	
2025-07-01T13:50:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached BROWSER as an AUT	
2025-07-01T13:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:78: End Step	When Browser is Up and Running
2025-07-01T13:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: Start Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2025-07-01T13:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select an Exam '<PatientName>' from Browser	
2025-07-01T13:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Browser Exam search field is performed	
2025-07-01T13:50:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Browser study table Search box selected successfully	
2025-07-01T13:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2025-07-01T13:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2025-07-01T13:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Browser Exam search field is Editable	
2025-07-01T13:50:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Browser Exam search field is performed	
2025-07-01T13:50:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Value entered in the search box successfully	
2025-07-01T13:50:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click action is performed on option menu 0/1	
2025-07-01T13:50:05	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Patient in the first row of the Browser study table selected successfully	
2025-07-01T13:50:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:79: End Step	And Select an Exam 'Sigmoid Test Pattern' from Browser
2025-07-01T13:50:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: Start Step	Then I Click on 'X' in the Image Management search area
2025-07-01T13:50:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I Click on 'X' in the Image Management search area	
2025-07-01T13:50:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Browser Exam search field is found	
2025-07-01T13:50:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: mouse click object Browser Exam search field is performed	
2025-07-01T13:50:10	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Performed mouseClick on Browser Exam search field	
2025-07-01T13:50:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:80: End Step	Then I Click on 'X' in the Image Management search area
2025-07-01T13:50:10	LOG       	End Example                   	
2025-07-01T13:50:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:75: End Scenario Outline	VERIFY_SIGMOID_TEST_PATTERN
2025-07-01T13:50:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_Browser/test.feature:1: End Feature	Browser
2025-07-01T13:50:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:50:10	END_TEST_CASE	End 'tst_Browser'             	End of test 'tst_Browser'
2025-07-01T13:50:10	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	  20
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	  20
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:50:10.649 PM
**********************************************************************************


