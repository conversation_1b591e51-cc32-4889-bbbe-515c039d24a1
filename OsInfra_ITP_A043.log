USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A043 ***************


Checking rmem_max value


Running command : '/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043'

Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
Time taken to execute script 28 ms

Expected output : 'none [mq-deadline] kyber bfq'

Actual output : 'none [mq-deadline] kyber bfq'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
ExpectedOutput = none [mq-deadline] kyber bfq
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A043 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A043 ***************


Checking rmem_max value


Running command : '/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043'

Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
Time taken to execute script 30 ms

Expected output : 'none [mq-deadline] kyber bfq'

Actual output : 'none [mq-deadline] kyber bfq'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
ExpectedOutput = none [mq-deadline] kyber bfq
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A043 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A043 ***************


Checking rmem_max value


Running command : '/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043'

Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
Time taken to execute script 29 ms

Expected output : 'none [mq-deadline] kyber bfq'

Actual output : 'none [mq-deadline] kyber bfq'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
ExpectedOutput = none [mq-deadline] kyber bfq
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A043 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A043 ***************


Checking rmem_max value


Running command : '/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043'

Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
Time taken to execute script 28 ms

Expected output : 'none [mq-deadline] kyber bfq'

Actual output : 'none [mq-deadline] kyber bfq'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
ExpectedOutput = none [mq-deadline] kyber bfq
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A043 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A043 ***************


Checking rmem_max value


Running command : '/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043'

Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
Time taken to execute script 28 ms

Expected output : 'none [mq-deadline] kyber bfq'

Actual output : 'none [mq-deadline] kyber bfq'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
ExpectedOutput = none [mq-deadline] kyber bfq
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A043 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A043 ***************


Checking rmem_max value


Running command : '/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043'

Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
Time taken to execute script 27 ms

Expected output : 'none [mq-deadline] kyber bfq'

Actual output : 'none [mq-deadline] kyber bfq'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
ExpectedOutput = none [mq-deadline] kyber bfq
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A043 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A043 ***************


Checking rmem_max value


Running command : '/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043'

Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
Time taken to execute script 27 ms

Expected output : 'none [mq-deadline] kyber bfq'

Actual output : 'none [mq-deadline] kyber bfq'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/scripts/OS_ITP_A018_A042.sh disk2 A042_A043
ExpectedOutput = none [mq-deadline] kyber bfq
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A043 ***************

Test Result : SUCCESS
