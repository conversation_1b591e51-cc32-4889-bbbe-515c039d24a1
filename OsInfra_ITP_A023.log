USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 4 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 15 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 16 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 18 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 17 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 16 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 17 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 15 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 16 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 17 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 12 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 15 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 17 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 6 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 4 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 4 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 3 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 6 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 4 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 6 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 6 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 4 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 4 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 14 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 16 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 14 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 4 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 6 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20270701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20270701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701*.xz /var/log/syslog-20270701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 | cut -f3-4 -d ' '
Time taken to execute script 17 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz | cut -f3-4 -d ' '
Time taken to execute script 16 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20270701 | cut -f3-4 -d ' '
Time taken to execute script 15 ms


Actual ownership : root root

File /var/log/syslog-20270701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20270701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20270701.xz is found with ownership sdc sdc
File /var/log/syslog-20270701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A023 ***************

Current Time : 20250701


Removing already existing syslog files : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Executing command : bash -c logger -t posevents bbbbb


Removing already existing file : /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c rm -f /export/home/<USER>/senovision/logfiles/pos/postraces-20250701* /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20250701*.xz /var/log/syslog-20250701*
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/postraces /tmp/postraces.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/postraces /tmp/postraces.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Taking backup of file : cp /etc/logrotate.d/syslog /tmp/syslog.bk
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp /etc/logrotate.d/syslog /tmp/syslog.bk
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Modifying file : sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running logrotate
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /usr/sbin/logrotate /etc/logrotate.conf
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : error: log /var/log/boot.log last rotated in the future -- rotation forced
Std Error Stream : error: skipping "/export/logfiles/senovision/sysperformance" because parent directory has insecure permissions (It's world writable or writable by group which is not "root") Set "su" directive in config file to tell logrotate which user/group should be used for rotation.
Std Error Stream : error: log /var/log/tomcat/catalina.out last rotated in the future -- rotation forced
Std Error Stream : error: log /var/log/tomcat/host-manager.log last rotated in the future -- rotation forced
Std Error Stream : error: log /var/log/tomcat/localhost.log last rotated in the future -- rotation forced
Std Error Stream : error: log /var/log/tomcat/manager.log last rotated in the future -- rotation forced


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/pos/postraces-20250701 | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/postraces-20250701 is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c ls -l /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20250701.xz | cut -f3-4 -d ' '
Time taken to execute script 6 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20250701.xz is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c ls -l /var/log/syslog-20250701 | cut -f3-4 -d ' '
Time taken to execute script 5 ms


Actual ownership : root root

File /var/log/syslog-20250701 is found with ownership root root


Restoring file : mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************


Restoring file : mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
Std Input Stream : 0
Std Input Stream : 0
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputRemoveSysLogFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "rm -f /var/log/syslog*"
InputTakeBackup = cp /etc/logrotate.d/PoseidonEvents.log /tmp/PoseidonEvents.log.bk,cp /etc/logrotate.d/postraces /tmp/postraces.bk,cp /etc/logrotate.d/syslog /tmp/syslog.bk
InputRunCommandInRemote = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,
InputModifyFiles = sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/PoseidonEvents.log;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/postraces;sed -i 's/size [0-9]\+/size 2/g' /etc/logrotate.d/syslog
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRestoreFiles = mv -f /tmp/PoseidonEvents.log.bk /etc/logrotate.d/PoseidonEvents.log;mv -f /tmp/postraces.bk  /etc/logrotate.d/postraces;mv -f /tmp/syslog.bk  /etc/logrotate.d/syslog
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/postraces-<yyyymmdd> /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-<yyyymmdd>.xz /var/log/syslog-<yyyymmdd>
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Performed file modifications successfully
Syslog server is restarted
Executed logrotate
File /export/home/<USER>/senovision/logfiles/pos/postraces-20250701 is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/PoseidonEvents.log-20250701.xz is found with ownership sdc sdc
File /var/log/syslog-20250701 is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A023 ***************

Test Result : SUCCESS
