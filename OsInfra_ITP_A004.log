USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 24 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 23 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 23 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 24 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 24 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 24 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 23 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 23 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 24 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 23 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 23 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 23 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 25 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 27 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 24 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 24 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A004 ***************


Checking installed Platform version

Executing : bash -c grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
Time taken to execute script 24 ms

Actual Platform version : PLATFORM-10.30.35

Expected Platform version : PLATFORM
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckPlatformVersion = grep -Po 'SWPlatform : \K\S+' /export/home/<USER>/softwareVersion/SWPlatform-REV.txt
ExpectedOutput = PLATFORM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected Platform version found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A004 ***************

Test Result : SUCCESS
