USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A015 ***************


Checking if 'vibr0' interface is displayed
Executing command : bash -c /sbin/ifconfig -a | grep vibr0
Std Error Stream : bash: /sbin/ifconfig: No such file or directory
Failed to execute the command
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckVibr0Interface = /sbin/ifconfig -a | grep vibr0
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

'vibr0' interface is not displayed
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A015 ***************

Test Result : SUCCESS
