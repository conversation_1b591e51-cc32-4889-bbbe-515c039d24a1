USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A037 ***************



Verifying that logfiles are archived if total log size if greater than 800MB
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A003
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A003 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/oldFiles/*
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/systemLogFiles/*
Std Input Stream : 
Std Input Stream : Checking postraces file
Std Input Stream : Executing:find /export/logfiles/senovision/pos/ -name 'postraces*'  | cut -d "/" -f6
Std Input Stream : 
Std Input Stream : Checking log size
Std Input Stream : 
Std Input Stream :  commandToGetLogSize :find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:du -cm /export/logfiles/senovision/pos/|grep total|cut -f1
Std Input Stream : Executing:touch /export/logfiles/senovision/trace.OSInfraTestSample.log;echo "This is a sample log file created in testcase automation" > /export/logfiles/senovision/trace.OSInfraTestSample.log
Std Input Stream : Executing:fallocate -l 810m /export/logfiles/senovision/trace.OSInfraTestSample.log
Std Input Stream : 
Std Input Stream :  commandToGetLogSize :find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:du -cm /export/logfiles/senovision/pos/|grep total|cut -f1
Std Input Stream : Logs Total Size : 906MB (Greater Than 800MB)
Std Input Stream : 
Std Input Stream : Running Log Cleaner
Std Input Stream : Executing:/export/home/<USER>/senovision/scripts/logCleaner.sh boot
Std Input Stream : Executing:stat /export/logfiles/senovision/LastlogCleaner.log | grep Modify |cut -c9-27
Std Input Stream : Log Cleaner Last Running Time : 2027-07-01 10:19:28
Std Input Stream : 
Std Input Stream : Waiting for 60 seconds
Std Input Stream : 
Std Input Stream : Checking new archives in /export/logfiles/senovision/oldFiles/
Std Input Stream : Executing:find /export/logfiles/senovision/oldFiles/ -name "trace*log.tar.gz" -newermt "2027-07-01 10:19:28" -ls  | awk '{print $11}'
Std Input Stream : New Log Archives : /export/logfiles/senovision/oldFiles/trace-070127-101928.log.tar.gz
Std Input Stream : 
Std Input Stream : Extracting archive to find postraces files
Std Input Stream : Executing:tar -tvf /export/logfiles/senovision/oldFiles/trace-070127-101928.log.tar.gz 'pos/postraces'|awk '{print $6}'|cut -d "/" -f 2
Std Input Stream : 
Std Input Stream : Checking old logs removed from /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "trace.*.log" ! -name "trace.UserEvents.log" -not -newermt "2027-07-01 10:19:28" -ls |awk '{print $11}' | grep -v -E '/protocol/|/technologist/|/annotationtemplate/|printableqc/|priorms/'
Std Input Stream : 
Std Input Stream : Checking new logs created in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "trace.*.log" -newermt "2027-07-01 10:19:28" -ls |awk '{print $11}'
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeAfterLogCleanerExecutionInSecs = 60
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Postraces found in /export/logfiles/senovision/pos/
Std Input Stream : Logs total size is greater than 800MB
Std Input Stream : New trace log archive created in /export/logfiles/senovision/oldFiles/
Std Input Stream : Extracted archive : /export/logfiles/senovision/oldFiles/trace-070127-101928.log.tar.gz
Std Input Stream : All PosTraces files found
Std Input Stream : Old trace log files are removed
Std Input Stream : 
Std Input Stream : New trace log files are created : 
Std Input Stream : /export/logfiles/senovision/trace.IdcETHDEM.log
Std Input Stream : /export/logfiles/senovision/trace.SliceStats.log
Std Input Stream : /export/logfiles/senovision/trace.Cgroup.log
Std Input Stream : /export/logfiles/senovision/trace.ServiceInterface.log
Std Input Stream : /export/logfiles/senovision/trace.UserEvents.log
Std Input Stream : /export/logfiles/senovision/trace.LoggingServerMonitor.log
Std Input Stream : /export/logfiles/senovision/trace.PltInhibitManagerMonitor.log
Std Input Stream : /export/logfiles/senovision/trace.SmapiComm.log
Std Input Stream : /export/logfiles/senovision/trace.eDelivery.log
Std Input Stream : /export/logfiles/senovision/trace.RSvPWrapper.log
Std Input Stream : /export/logfiles/senovision/trace.eDeliveryResume.log
Std Input Stream : /export/logfiles/senovision/trace.AxisPCInhibitC++.log
Std Input Stream : /export/logfiles/senovision/trace.SWDownloadEvents.log
Std Input Stream : /export/logfiles/senovision/trace.SensorDataComm.log
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A003 ***************
Executing command : bash -c (/usr/bin/sleep 10;/usr/bin/sudo /sbin/reboot) &
Reboot executed successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunLogCleaner = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A003
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Log archive created and postraces found in archive
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A037 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A037 ***************



Verifying that logfiles are archived if total log size if greater than 800MB
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A003
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A003 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/oldFiles/*
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/systemLogFiles/*
Std Input Stream : 
Std Input Stream : Checking postraces file
Std Input Stream : Executing:find /export/logfiles/senovision/pos/ -name 'postraces*'  | cut -d "/" -f6
Std Input Stream : 
Std Input Stream : Checking log size
Std Input Stream : 
Std Input Stream :  commandToGetLogSize :find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:du -cm /export/logfiles/senovision/pos/|grep total|cut -f1
Std Input Stream : Executing:touch /export/logfiles/senovision/trace.OSInfraTestSample.log;echo "This is a sample log file created in testcase automation" > /export/logfiles/senovision/trace.OSInfraTestSample.log
Std Input Stream : Executing:fallocate -l 810m /export/logfiles/senovision/trace.OSInfraTestSample.log
Std Input Stream : 
Std Input Stream :  commandToGetLogSize :find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:du -cm /export/logfiles/senovision/pos/|grep total|cut -f1
Std Input Stream : Logs Total Size : 910MB (Greater Than 800MB)
Std Input Stream : 
Std Input Stream : Running Log Cleaner
Std Input Stream : Executing:/export/home/<USER>/senovision/scripts/logCleaner.sh boot
Std Input Stream : Executing:stat /export/logfiles/senovision/LastlogCleaner.log | grep Modify |cut -c9-27
Std Input Stream : Log Cleaner Last Running Time : 2027-07-01 12:39:15
Std Input Stream : 
Std Input Stream : Waiting for 60 seconds
Std Input Stream : 
Std Input Stream : Checking new archives in /export/logfiles/senovision/oldFiles/
Std Input Stream : Executing:find /export/logfiles/senovision/oldFiles/ -name "trace*log.tar.gz" -newermt "2027-07-01 12:39:15" -ls  | awk '{print $11}'
Std Input Stream : New Log Archives : /export/logfiles/senovision/oldFiles/trace-070127-123915.log.tar.gz
Std Input Stream : 
Std Input Stream : Extracting archive to find postraces files
Std Input Stream : Executing:tar -tvf /export/logfiles/senovision/oldFiles/trace-070127-123915.log.tar.gz 'pos/postraces'|awk '{print $6}'|cut -d "/" -f 2
Std Input Stream : 
Std Input Stream : Checking old logs removed from /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "trace.*.log" ! -name "trace.UserEvents.log" -not -newermt "2027-07-01 12:39:15" -ls |awk '{print $11}' | grep -v -E '/protocol/|/technologist/|/annotationtemplate/|printableqc/|priorms/'
Std Input Stream : 
Std Input Stream : Checking new logs created in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "trace.*.log" -newermt "2027-07-01 12:39:15" -ls |awk '{print $11}'
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeAfterLogCleanerExecutionInSecs = 60
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Postraces found in /export/logfiles/senovision/pos/
Std Input Stream : Logs total size is greater than 800MB
Std Input Stream : New trace log archive created in /export/logfiles/senovision/oldFiles/
Std Input Stream : Extracted archive : /export/logfiles/senovision/oldFiles/trace-070127-123915.log.tar.gz
Std Input Stream : All PosTraces files found
Std Input Stream : Old trace log files are removed
Std Input Stream : 
Std Input Stream : New trace log files are created : 
Std Input Stream : /export/logfiles/senovision/trace.IdcETHDEM.log
Std Input Stream : /export/logfiles/senovision/trace.SliceStats.log
Std Input Stream : /export/logfiles/senovision/trace.Cgroup.log
Std Input Stream : /export/logfiles/senovision/trace.ServiceInterface.log
Std Input Stream : /export/logfiles/senovision/trace.UserEvents.log
Std Input Stream : /export/logfiles/senovision/trace.LoggingServerMonitor.log
Std Input Stream : /export/logfiles/senovision/trace.PltInhibitManagerMonitor.log
Std Input Stream : /export/logfiles/senovision/trace.SmapiComm.log
Std Input Stream : /export/logfiles/senovision/trace.eDelivery.log
Std Input Stream : /export/logfiles/senovision/trace.RSvPWrapper.log
Std Input Stream : /export/logfiles/senovision/trace.eDeliveryResume.log
Std Input Stream : /export/logfiles/senovision/trace.AxisPCInhibitC++.log
Std Input Stream : /export/logfiles/senovision/trace.SWDownloadEvents.log
Std Input Stream : /export/logfiles/senovision/trace.SensorDataComm.log
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A003 ***************
Executing command : bash -c (/usr/bin/sleep 10;/usr/bin/sudo /sbin/reboot) &
Reboot executed successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunLogCleaner = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A003
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Log archive created and postraces found in archive
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A037 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A037 ***************



Verifying that logfiles are archived if total log size if greater than 800MB
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A003
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A003 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/oldFiles/*
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/systemLogFiles/*
Std Input Stream : 
Std Input Stream : Checking postraces file
Std Input Stream : Executing:find /export/logfiles/senovision/pos/ -name 'postraces*'  | cut -d "/" -f6
Std Input Stream : 
Std Input Stream : Checking log size
Std Input Stream : 
Std Input Stream :  commandToGetLogSize :find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:du -cm /export/logfiles/senovision/pos/|grep total|cut -f1
Std Input Stream : Executing:touch /export/logfiles/senovision/trace.OSInfraTestSample.log;echo "This is a sample log file created in testcase automation" > /export/logfiles/senovision/trace.OSInfraTestSample.log
Std Input Stream : Executing:fallocate -l 810m /export/logfiles/senovision/trace.OSInfraTestSample.log
Std Input Stream : 
Std Input Stream :  commandToGetLogSize :find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:du -cm /export/logfiles/senovision/pos/|grep total|cut -f1
Std Input Stream : Logs Total Size : 857MB (Greater Than 800MB)
Std Input Stream : 
Std Input Stream : Running Log Cleaner
Std Input Stream : Executing:/export/home/<USER>/senovision/scripts/logCleaner.sh boot
Std Input Stream : Executing:stat /export/logfiles/senovision/LastlogCleaner.log | grep Modify |cut -c9-27
Std Input Stream : Log Cleaner Last Running Time : 2025-07-01 14:06:49
Std Input Stream : 
Std Input Stream : Waiting for 60 seconds
Std Input Stream : 
Std Input Stream : Checking new archives in /export/logfiles/senovision/oldFiles/
Std Input Stream : Executing:find /export/logfiles/senovision/oldFiles/ -name "trace*log.tar.gz" -newermt "2025-07-01 14:06:49" -ls  | awk '{print $11}'
Std Input Stream : New Log Archives : /export/logfiles/senovision/oldFiles/trace-070125-140649.log.tar.gz
Std Input Stream : 
Std Input Stream : Extracting archive to find postraces files
Std Input Stream : Executing:tar -tvf /export/logfiles/senovision/oldFiles/trace-070125-140649.log.tar.gz 'pos/postraces'|awk '{print $6}'|cut -d "/" -f 2
Std Input Stream : 
Std Input Stream : Checking old logs removed from /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "trace.*.log" ! -name "trace.UserEvents.log" -not -newermt "2025-07-01 14:06:49" -ls |awk '{print $11}' | grep -v -E '/protocol/|/technologist/|/annotationtemplate/|printableqc/|priorms/'
Std Input Stream : 
Std Input Stream : Checking new logs created in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "trace.*.log" -newermt "2025-07-01 14:06:49" -ls |awk '{print $11}'
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeAfterLogCleanerExecutionInSecs = 60
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Postraces found in /export/logfiles/senovision/pos/
Std Input Stream : Logs total size is greater than 800MB
Std Input Stream : New trace log archive created in /export/logfiles/senovision/oldFiles/
Std Input Stream : Extracted archive : /export/logfiles/senovision/oldFiles/trace-070125-140649.log.tar.gz
Std Input Stream : All PosTraces files found
Std Input Stream : Old trace log files are removed
Std Input Stream : 
Std Input Stream : New trace log files are created : 
Std Input Stream : /export/logfiles/senovision/protocol/logs/trace.importandexportprotocols.log
Std Input Stream : /export/logfiles/senovision/systemLogFiles/trace.UserEvents.log
Std Input Stream : /export/logfiles/senovision/trace.IdcETHDEM.log
Std Input Stream : /export/logfiles/senovision/trace.ServiceInterface.log
Std Input Stream : /export/logfiles/senovision/trace.UserEvents.log
Std Input Stream : /export/logfiles/senovision/trace.LoggingServerMonitor.log
Std Input Stream : /export/logfiles/senovision/trace.PltInhibitManagerMonitor.log
Std Input Stream : /export/logfiles/senovision/trace.SmapiComm.log
Std Input Stream : /export/logfiles/senovision/trace.eDelivery.log
Std Input Stream : /export/logfiles/senovision/trace.RSvPWrapper.log
Std Input Stream : /export/logfiles/senovision/trace.eDeliveryResume.log
Std Input Stream : /export/logfiles/senovision/trace.AxisPCInhibitC++.log
Std Input Stream : /export/logfiles/senovision/trace.SWDownloadEvents.log
Std Input Stream : /export/logfiles/senovision/trace.SensorDataComm.log
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A003 ***************
Executing command : bash -c (/usr/bin/sleep 10;/usr/bin/sudo /sbin/reboot) &
Reboot executed successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunLogCleaner = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A003
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Log archive created and postraces found in archive
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A037 ***************

Test Result : SUCCESS
