#!/usr/bin/env python3
"""
Log Analyzer Script
Converts test log files into a beautiful HTML report with charts and visualizations.
Supports both regular test logs and SquishRunner logs.
"""

import os
import re
import glob
from datetime import datetime
from collections import defaultdict, Counter

class LogAnalyzer:
    def __init__(self, log_directory="/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration"):
        self.log_directory = log_directory
        self.test_results = []
        self.squish_results = []
        self.summary_stats = {
            'total_files': 0,
            'total_tests': 0,
            'total_passes': 0,
            'total_failures': 0,
            'success_rate': 0.0
        }
    
    def parse_regular_logs(self):
        """Parse regular test log files (OsInfra_ITP_A*.log)"""
        pattern = os.path.join(self.log_directory, "OsInfra_ITP_A*.log")
        log_files = [f for f in glob.glob(pattern) if not f.endswith("_SquishRunner.log")]
        
        print(f"Debug: Pattern used: {pattern}")
        print(f"Debug: All matches from glob: {glob.glob(pattern)}")
        print(f"Found {len(log_files)} regular log files")
        
        for log_file in log_files:
            print(f"Processing regular log: {log_file}")
            test_name = os.path.basename(log_file).replace('.log', '')
            
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Count test executions and results using simple pattern matching
                pass_matches = re.findall(r'TestCase PASS/FAIL = PASS', content)
                fail_matches = re.findall(r'TestCase PASS/FAIL = FAILURE', content)
                success_matches = re.findall(r'Test Result : SUCCESS', content)
                failure_matches = re.findall(r'Test Result : FAILURE', content)
                
                print(f"  Debug: pass_matches={len(pass_matches)}, fail_matches={len(fail_matches)}")
                print(f"  Debug: success_matches={len(success_matches)}, failure_matches={len(failure_matches)}")
                
                # Get test description if available
                description_match = re.search(r'Started Executing the Test Case - (.+?) \*', content)
                description = description_match.group(1) if description_match else test_name
                
                # Determine overall status and extract error messages
                total_executions = len(success_matches) + len(failure_matches)
                detailed_error_list = []

                if total_executions > 0:
                    status = 'PASS' if len(failure_matches) == 0 else 'FAIL'
                    success_rate = (len(success_matches) / total_executions) * 100
                    
                    # If failed, extract ActualOutput content for each failed test case
                    if status == 'FAIL':
                        # Find all ActualOutput sections that come before "TestCase PASS/FAIL = FAILURE"
                        failure_blocks = re.findall(r'ActualOutput = \s*\n(.*?)\n.*?TestCase PASS/FAIL = FAILURE', content, re.DOTALL)
                        
                        for i, actual_output in enumerate(failure_blocks):
                            actual_output = actual_output.strip()
                            
                            # Clean up the actual output
                            if actual_output and "Warning:Update the actual output Properly" not in actual_output:
                                # Remove extra whitespace and newlines
                                cleaned_output = ' '.join(actual_output.split())
                                if cleaned_output:
                                    detailed_error_list.append({
                                        'test_case': f"{test_name} - Execution {i+1}",
                                        'error_message': cleaned_output
                                    })
                        
                        # If no failure blocks found, create a generic error
                        if not detailed_error_list:
                            detailed_error_list = [{'test_case': test_name, 'error_message': f"Test execution failed - check {test_name} log for details"}]
                else:
                    # No test executions found - this is a FAIL
                    status = 'FAIL'
                    success_rate = 0
                    detailed_error_list = [{'test_case': test_name, 'error_message': "No test executions found"}]

                self.test_results.append({
                    'test_name': test_name,
                    'description': description,
                    'status': status,
                    'executions': total_executions,
                    'passes': len(success_matches),
                    'failures': len(failure_matches),
                    'success_rate': success_rate,
                    'detailed_errors': detailed_error_list,
                    'file_path': log_file
                })
                
            except Exception as e:
                print(f"Error parsing {log_file}: {e}")
    
    def parse_squish_logs(self):
        """Parse SquishRunner log files"""
        pattern = os.path.join(self.log_directory, "*_SquishRunner.log")
        squish_files = glob.glob(pattern)
        
        print(f"Debug: Squish pattern used: {pattern}")
        print(f"Debug: All squish matches from glob: {squish_files}")
        print(f"Found {len(squish_files)} SquishRunner log files")
        
        for log_file in squish_files:
            print(f"Processing squish log: {log_file}")
            test_name = os.path.basename(log_file).replace('_SquishRunner.log', '')
            
            try:
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # Find all summary sections
                summary_pattern = r'Summary:\s*\n(?:.*\n)*?Number of Test Cases:\s*(\d+)\s*\n.*?Number of Tests:\s*(\d+)\s*\n.*?Number of Errors:\s*(\d+)\s*\n.*?Number of Fatals:\s*(\d+)\s*\n.*?Number of Fails:\s*(\d+)\s*\n.*?Number of Passes:\s*(\d+)\s*\n.*?Number of Expected Fails:\s*(\d+)\s*\n.*?Number of Unexpected Passes:\s*(\d+)\s*\n.*?Number of Warnings:\s*(\d+)'
                
                summaries = re.findall(summary_pattern, content, re.MULTILINE)
                print(f"  Debug: Found {len(summaries)} summary sections")
                
                if summaries:
                    # Aggregate all summary data
                    total_test_cases = sum(int(s[0]) for s in summaries)
                    total_tests = sum(int(s[1]) for s in summaries)
                    total_errors = sum(int(s[2]) for s in summaries)
                    total_fatals = sum(int(s[3]) for s in summaries)
                    total_fails = sum(int(s[4]) for s in summaries)
                    total_passes = sum(int(s[5]) for s in summaries)
                    total_expected_fails = sum(int(s[6]) for s in summaries)
                    total_unexpected_passes = sum(int(s[7]) for s in summaries)
                    total_warnings = sum(int(s[8]) for s in summaries)
                    
                    # Determine overall status and extract scenario-specific error messages
                    detailed_error_list = []

                    if total_errors > 0 or total_fatals > 0 or total_fails > 0:
                        status = 'FAIL'

                        # Extract scenario names and their specific error messages
                        scenario_sections = re.findall(r'Scenario Name - ([^*\n]+).*?\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*(.*?)(?=\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*|$)', content, re.DOTALL)
                        
                        for scenario_name, scenario_content in scenario_sections:
                            # Look for ERROR messages in this scenario
                            error_matches = re.findall(r'ERROR\s*.*?ERROR\s*:(.*?)$', scenario_content, re.MULTILINE)
                            if error_matches:
                                for error_msg in error_matches:
                                    error_msg = error_msg.strip()
                                    if error_msg:
                                        detailed_error_list.append({
                                            'test_case': f'Scenario: {scenario_name.strip()}',
                                            'error_message': f'ERROR: {error_msg}'
                                        })
                            
                            # Also look for Script Error RuntimeError messages
                            script_error_matches = re.findall(r'Script Error\s*RuntimeError:\s*(.*?)$', scenario_content, re.MULTILINE)
                            if script_error_matches:
                                for error_msg in script_error_matches:
                                    error_msg = error_msg.strip()
                                    if error_msg:
                                        detailed_error_list.append({
                                            'test_case': f'Scenario: {scenario_name.strip()}',
                                            'error_message': f'RuntimeError: {error_msg}'
                                        })
                        
                        # If no scenario-specific errors found, look for general errors
                        if not detailed_error_list:
                            # Look for ERROR messages anywhere in the content
                            general_errors = re.findall(r'ERROR\s*.*?ERROR\s*:(.*?)$', content, re.MULTILINE)
                            for i, error_msg in enumerate(general_errors):
                                error_msg = error_msg.strip()
                                if error_msg:
                                    detailed_error_list.append({
                                        'test_case': f'Test Case {i+1}',
                                        'error_message': f'ERROR: {error_msg}'
                                    })
                            
                            # Also look for Script Error RuntimeError messages
                            script_errors = re.findall(r'Script Error\s*RuntimeError:\s*(.*?)$', content, re.MULTILINE)
                            for i, error_msg in enumerate(script_errors):
                                error_msg = error_msg.strip()
                                if error_msg:
                                    detailed_error_list.append({
                                        'test_case': f'Test Case {i+1}',
                                        'error_message': f'RuntimeError: {error_msg}'
                                    })
                        
                        # If still no errors found, use generic message
                        if not detailed_error_list:
                            detailed_error_list = [{'test_case': 'Unknown', 'error_message': f"Test execution failed - Errors: {total_errors}, Fatals: {total_fatals}, Fails: {total_fails}"}]
                    elif total_passes > 0:
                        status = 'PASS'
                        detailed_error_list = []
                    else:
                        # No test results found - this is a FAIL
                        status = 'FAIL'
                        detailed_error_list = [{'test_case': 'Unknown', 'error_message': "No test results found"}]

                    success_rate = (total_passes / max(total_tests, 1)) * 100 if total_tests > 0 else 0

                    self.squish_results.append({
                        'test_name': test_name,
                        'status': status,
                        'test_cases': total_test_cases,
                        'tests': total_tests,
                        'passes': total_passes,
                        'fails': total_fails,
                        'errors': total_errors,
                        'fatals': total_fatals,
                        'warnings': total_warnings,
                        'success_rate': success_rate,
                        'detailed_errors': detailed_error_list,
                        'file_path': log_file
                    })
                
            except Exception as e:
                print(f"Error parsing {log_file}: {e}")
    
    def calculate_summary_stats(self):
        """Calculate overall summary statistics"""
        all_results = self.test_results + self.squish_results
        
        self.summary_stats['total_files'] = len(all_results)
        
        # For regular tests
        regular_passes = sum(1 for r in self.test_results if r['status'] == 'PASS')
        regular_failures = sum(1 for r in self.test_results if r['status'] == 'FAIL')
        
        # For squish tests
        squish_passes = sum(1 for r in self.squish_results if r['status'] == 'PASS')
        squish_failures = sum(1 for r in self.squish_results if r['status'] == 'FAIL')
        
        self.summary_stats['total_passes'] = regular_passes + squish_passes
        self.summary_stats['total_failures'] = regular_failures + squish_failures
        self.summary_stats['total_tests'] = self.summary_stats['total_passes'] + self.summary_stats['total_failures']
        
        # Calculate success rate
        if self.summary_stats['total_tests'] > 0:
            self.summary_stats['success_rate'] = (self.summary_stats['total_passes'] / self.summary_stats['total_tests']) * 100
    
    def analyze_logs(self):
        """Main method to analyze all log files"""
        print(f"Starting log analysis in directory: {self.log_directory}")
        
        # Check if directory exists
        if not os.path.exists(self.log_directory):
            print(f"Error: Directory {self.log_directory} does not exist!")
            print("Please check the path or update the log_directory parameter.")
            return None, [], []
        
        self.parse_regular_logs()
        self.parse_squish_logs()
        self.calculate_summary_stats()
        print(f"Analysis complete. Found {len(self.test_results)} regular tests and {len(self.squish_results)} squish tests.")
        return self.summary_stats, self.test_results, self.squish_results

def generate_html_report(summary_stats, test_results, squish_results, output_file=None):
    """Generate a ultra-modern beautiful HTML report with elegant charts"""
    
    if not summary_stats:
        print("No data to generate report")
        return
    
    # Set default output file path if not provided
    if output_file is None:
        output_dir = "/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/"
        # Create directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, "test_report.html")
    
    # Prepare data for charts
    status_data = {
        'passes': summary_stats['total_passes'],
        'failures': summary_stats['total_failures']
    }
    
    # Test type distribution
    test_type_data = {
        'Regular Tests': len(test_results),
        'Squish Tests': len(squish_results)
    }
    
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Execution Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {{
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }}

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
            color: var(--text-primary);
        }}
        
        .dashboard {{
            max-width: 1000px;
            margin: 0 auto;
            background: var(--bg-primary);
            border-radius: 24px;
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }}
        
        .header {{
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: white;
            padding: 1rem 2rem;
            position: relative;
            overflow: hidden;
        }}
        
        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }}
        
        @keyframes shimmer {{
            0%, 100% {{ transform: translateX(-100%); }}
            50% {{ transform: translateX(100%); }}
        }}
        
        .header-content {{
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .header h1 {{
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            background: linear-gradient(45deg, #ffffff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .header .subtitle {{
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 400;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }}
        
        .stats-overview {{
            padding: 0.25rem;
            background: var(--bg-secondary);
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }}
        
        .stat-card {{
            background: var(--bg-primary);
            padding: 0.75rem;
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }}
        
        .stat-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-accent, var(--info-color));
            transition: height 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }}
        
        .stat-card:hover::before {{
            height: 8px;
        }}
        
        .stat-card.success {{ --card-accent: var(--success-color); }}
        .stat-card.error {{ --card-accent: var(--error-color); }}
        .stat-card.info {{ --card-accent: var(--info-color); }}
        .stat-card.warning {{ --card-accent: var(--warning-color); }}
        
        .stat-header {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }}
        
        .stat-icon {{
            width: 28px;
            height: 28px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            color: white;
            background: var(--card-accent, var(--info-color));
        }}
        
        .stat-number {{
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--card-accent, var(--info-color));
            line-height: 1;
        }}
        
        .stat-label {{
            color: var(--text-secondary);
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-top: 0.5rem;
        }}
        
        .charts-section {{
            padding: 0.25rem;
            background: var(--bg-primary);
        }}
        
        .charts-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-bottom: 0.25rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }}
        
        @media (max-width: 1024px) {{
            .charts-grid {{
                grid-template-columns: 1fr;
            }}
        }}
        
        .chart-container {{
            background: var(--bg-primary);
            padding: 1rem;
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            position: relative;
            height: 200px;
            display: flex;
            flex-direction: column;
        }}
        
        .chart-header {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            flex-shrink: 0;
        }}
        
        .chart-title {{
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }}
        
        .chart-wrapper {{
            position: relative;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 140px;
        }}
        
        .chart-canvas {{
            max-height: 140px !important;
            max-width: 100% !important;
        }}
        
        .insights {{
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 2rem;
            margin: 2rem;
            border-radius: 16px;
            border-left: 4px solid var(--info-color);
        }}
        
        .insights h3 {{
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }}
        
        .insight-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }}
        
        .insight-item {{
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: var(--shadow-sm);
        }}
        
        .table-section {{
            padding: 1rem;
            background: var(--bg-secondary);
        }}
        
        .section-header {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }}
        
        .section-title {{
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }}
        
        .table-container {{
            background: var(--bg-primary);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
        }}
        
        .table-wrapper {{
            overflow-x: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
        }}
        
        th {{
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            color: white;
            padding: 0.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }}
        
        td {{
            padding: 0.5rem;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.75rem;
        }}
        
        tr:hover {{
            background: var(--bg-secondary);
        }}
        
        tr:last-child td {{
            border-bottom: none;
        }}
        
        .status-badge {{
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }}
        
        .status-pass {{
            background: #dcfce7;
            color: #166534;
        }}
        
        .status-fail {{
            background: #fee2e2;
            color: #991b1b;
        }}
        
        .status-unknown {{
            background: #fef3c7;
            color: #92400e;
        }}
        
        .footer {{
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            text-align: center;
            padding: 2rem;
            font-size: 0.875rem;
            opacity: 0.9;
        }}
        
        .progress-ring {{
            width: 60px;
            height: 60px;
        }}
        
        .progress-ring__circle {{
            stroke-width: 4;
            fill: transparent;
        }}
        
        .empty-state {{
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }}
        
        .empty-state i {{
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }}
        
        /* Expandable row styles */
        .expandable-row {{
            cursor: pointer;
            transition: background-color 0.2s ease;
        }}
        
        .expandable-row:hover {{
            background-color: #f8fafc !important;
        }}
        
        .expandable-row.expanded {{
            background-color: #f1f5f9 !important;
        }}
        
        .expand-icon {{
            transition: transform 0.3s ease;
            margin-right: 0.5rem;
        }}
        
        .expand-icon.expanded {{
            transform: rotate(90deg);
        }}
        
        .error-details-row {{
            background-color: #fef2f2 !important;
            border-left: 4px solid #ef4444;
        }}
        
        .error-details-cell {{
            padding: 0 !important;
        }}
        
        .error-details-content {{
            padding: 1rem;
            background: linear-gradient(135deg, #fef2f2 0%, #fdf2f8 100%);
            border-radius: 8px;
            margin: 0.5rem;
        }}
        
        .error-list-container {{
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }}
        
        .error-item-card {{
            background: white;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(239, 68, 68, 0.1);
            transition: all 0.3s ease;
        }}
        
        .error-item-card:hover {{
            box-shadow: 0 4px 8px rgba(239, 68, 68, 0.15);
            transform: translateY(-1px);
        }}
        
        .error-item-header {{
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }}
        
        .error-item-number {{
            background: #ef4444;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: 600;
            flex-shrink: 0;
        }}
        
        .error-item-title {{
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }}
        
        .error-item-message {{
            color: #6b7280;
            font-size: 0.85rem;
            line-height: 1.5;
            padding-left: 2rem;
            font-family: 'Inter', monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
        }}
        
        @media (max-width: 1200px) {{
            .stats-grid {{
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }}
            
            .error-details {{
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 90%;
                max-width: 700px;
                max-height: 70vh;
                border-radius: 12px;
                min-width: auto;
            }}
        }}
        
        @media (max-width: 768px) {{
            .stats-grid {{
                grid-template-columns: 1fr;
            }}
            
            .header-content {{
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }}
            
            .header h1 {{
                font-size: 1.75rem;
            }}
            
            .dashboard {{
                border-radius: 12px;
                margin: 0.5rem;
            }}
        }}
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <div class="header-content">
                <h1><i class="fas fa-vial"></i> OSITP Test Results</h1>
                <p class="subtitle">
                    <i class="fas fa-calendar-alt"></i>
                    Generated on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}
                </p>
            </div>
        </div>

        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-card info">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-number">{summary_stats['total_files']}</div>
                    </div>
                    <div class="stat-label">Total Test Cases</div>
                </div>
                
                <div class="stat-card success">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number">{summary_stats['total_passes']}</div>
                    </div>
                    <div class="stat-label">Passed Tests</div>
                </div>
                
                <div class="stat-card error">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-number">{summary_stats['total_failures']}</div>
                    </div>
                    <div class="stat-label">Failed Tests</div>
                </div>
                
                <div class="stat-card warning">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-number">{summary_stats['success_rate']:.1f}%</div>
                    </div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>
        </div>

        <div class="charts-section">
            <div class="charts-grid">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-chart-pie"></i>
                            Test Results Overview
                        </h3>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="resultsChart" class="chart-canvas"></canvas>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-chart-bar"></i>
                            Test Type Distribution
                        </h3>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="typeChart" class="chart-canvas"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-table"></i>
                    Regular Test Results
                </h2>
            </div>
            <div class="table-container">
                <div class="table-wrapper">
                    <table>
                        <thead>
                            <tr>
                                <th><i class="fas fa-tag"></i> Test Name</th>
                                <th><i class="fas fa-traffic-light"></i> Status</th>
                                <th><i class="fas fa-play"></i> Executions</th>
                                <th><i class="fas fa-check"></i> Passes</th>
                                <th><i class="fas fa-times"></i> Failures</th>
                                <th><i class="fas fa-percentage"></i> Success Rate</th>
                                <th><i class="fas fa-exclamation-triangle"></i> Error Details</th>
                            </tr>
                        </thead>
                        <tbody>"""

    # Add regular test results
    if not test_results:
        html_content += """
                            <tr>
                                <td colspan="7" class="empty-state">
                                    <i class="fas fa-inbox"></i>
                                    <p>No regular test results found</p>
                                </td>
                            </tr>"""
    else:
        for i, test in enumerate(test_results):
            status_class = 'status-pass' if test['status'] == 'PASS' else 'status-fail' if test['status'] == 'FAIL' else 'status-unknown'
            
            # Create the main row (expandable if there are errors)
            if test['status'] != 'PASS' and test.get('detailed_errors'):
                detailed_errors = test['detailed_errors']
                if detailed_errors:
                    # Main expandable row for failed tests
                    html_content += f"""
                            <tr class="expandable-row" onclick="toggleRowExpansion('reg-{i}')">
                                <td>
                                    <div style="display: flex; align-items: center;">
                                        <i class="fas fa-chevron-right expand-icon" id="reg-{i}-icon"></i>
                                        <strong>{test['test_name']}</strong>
                                    </div>
                                </td>
                                <td><span class="status-badge {status_class}">{test['status']}</span></td>
                                <td>{test['executions']}</td>
                                <td><span style="color: var(--success-color); font-weight: 600;">{test['passes']}</span></td>
                                <td><span style="color: var(--error-color); font-weight: 600;">{test['failures']}</span></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <span style="font-weight: 600;">{test['success_rate']:.1f}%</span>
                                        <div style="width: 60px; height: 6px; background: #e5e7eb; border-radius: 3px; overflow: hidden;">
                                            <div style="width: {test['success_rate']:.1f}%; height: 100%; background: var(--success-color); transition: width 0.3s ease;"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span style="color: var(--error-color); font-weight: 600;">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        {len(detailed_errors)} Failed Test Case(s)
                                    </span>
                                </td>
                            </tr>
                            <tr class="error-details-row" id="reg-{i}-details" style="display: none;">
                                <td colspan="7" class="error-details-cell">
                                    <div class="error-details-content">
                                        <h4 style="margin: 0 0 1rem 0; color: #374151; font-size: 1rem;">
                                            <i class="fas fa-list-alt"></i> Failed Test Cases Details
                                        </h4>
                                        <div class="error-list-container">"""
                    
                    for j, error in enumerate(detailed_errors):
                        html_content += f"""
                                            <div class="error-item-card">
                                                <div class="error-item-header">
                                                    <span class="error-item-number">{j+1}</span>
                                                    <span class="error-item-title">{error['test_case']}</span>
                                                </div>
                                                <div class="error-item-message">{error['error_message']}</div>
                                            </div>"""
                    
                    html_content += """
                                        </div>
                                    </div>
                                </td>
                            </tr>"""
                else:
                    # Regular row for failed tests without detailed errors
                    html_content += f"""
                            <tr>
                                <td><strong>{test['test_name']}</strong></td>
                                <td><span class="status-badge {status_class}">{test['status']}</span></td>
                                <td>{test['executions']}</td>
                                <td><span style="color: var(--success-color); font-weight: 600;">{test['passes']}</span></td>
                                <td><span style="color: var(--error-color); font-weight: 600;">{test['failures']}</span></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <span style="font-weight: 600;">{test['success_rate']:.1f}%</span>
                                        <div style="width: 60px; height: 6px; background: #e5e7eb; border-radius: 3px; overflow: hidden;">
                                            <div style="width: {test['success_rate']:.1f}%; height: 100%; background: var(--success-color); transition: width 0.3s ease;"></div>
                                        </div>
                                    </div>
                                </td>
                                <td><small style="color: var(--text-secondary);">No specific error details available</small></td>
                            </tr>"""
            else:
                # Regular row for passed tests
                html_content += f"""
                            <tr>
                                <td><strong>{test['test_name']}</strong></td>
                                <td><span class="status-badge {status_class}">{test['status']}</span></td>
                                <td>{test['executions']}</td>
                                <td><span style="color: var(--success-color); font-weight: 600;">{test['passes']}</span></td>
                                <td><span style="color: var(--error-color); font-weight: 600;">{test['failures']}</span></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <span style="font-weight: 600;">{test['success_rate']:.1f}%</span>
                                        <div style="width: 60px; height: 6px; background: #e5e7eb; border-radius: 3px; overflow: hidden;">
                                            <div style="width: {test['success_rate']:.1f}%; height: 100%; background: var(--success-color); transition: width 0.3s ease;"></div>
                                        </div>
                                    </div>
                                </td>
                                <td><span style="color: var(--success-color);">? No errors</span></td>
                            </tr>"""

    html_content += """
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-robot"></i>
                    Squish Test Results
                </h2>
            </div>
            <div class="table-container">
                <div class="table-wrapper">
                    <table>
                        <thead>
                            <tr>
                                <th><i class="fas fa-tag"></i> Test Name</th>
                                <th><i class="fas fa-traffic-light"></i> Status</th>
                                <th><i class="fas fa-list"></i> Test Cases</th>
                                <th><i class="fas fa-vial"></i> Tests</th>
                                <th><i class="fas fa-check"></i> Passes</th>
                                <th><i class="fas fa-times"></i> Fails</th>
                                <th><i class="fas fa-percentage"></i> Success Rate</th>
                                <th><i class="fas fa-exclamation-triangle"></i> Error Details</th>
                            </tr>
                        </thead>
                        <tbody>"""

    # Add squish test results
    if not squish_results:
        html_content += """
                            <tr>
                                <td colspan="8" class="empty-state">
                                    <i class="fas fa-inbox"></i>
                                    <p>No Squish test results found</p>
                                </td>
                            </tr>"""
    else:
        for i, test in enumerate(squish_results):
            status_class = 'status-pass' if test['status'] == 'PASS' else 'status-fail' if test['status'] == 'FAIL' else 'status-unknown'
            
            # Create the main row (expandable if there are errors)
            if test['status'] != 'PASS' and test.get('detailed_errors'):
                detailed_errors = test['detailed_errors']
                if detailed_errors:
                    # Main expandable row for failed tests
                    html_content += f"""
                            <tr class="expandable-row" onclick="toggleRowExpansion('squish-{i}')">
                                <td>
                                    <div style="display: flex; align-items: center;">
                                        <i class="fas fa-chevron-right expand-icon" id="squish-{i}-icon"></i>
                                        <strong>{test['test_name']}</strong>
                                    </div>
                                </td>
                                <td><span class="status-badge {status_class}">{test['status']}</span></td>
                                <td>{test['test_cases']}</td>
                                <td>{test['tests']}</td>
                                <td><span style="color: var(--success-color); font-weight: 600;">{test['passes']}</span></td>
                                <td><span style="color: var(--error-color); font-weight: 600;">{test['fails']}</span></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <span style="font-weight: 600;">{test['success_rate']:.1f}%</span>
                                        <div style="width: 60px; height: 6px; background: #e5e7eb; border-radius: 3px; overflow: hidden;">
                                            <div style="width: {test['success_rate']:.1f}%; height: 100%; background: var(--success-color); transition: width 0.3s ease;"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span style="color: var(--error-color); font-weight: 600;">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        {len(detailed_errors)} Failed Test Case(s)
                                    </span>
                                </td>
                            </tr>
                            <tr class="error-details-row" id="squish-{i}-details" style="display: none;">
                                <td colspan="8" class="error-details-cell">
                                    <div class="error-details-content">
                                        <h4 style="margin: 0 0 1rem 0; color: #374151; font-size: 1rem;">
                                            <i class="fas fa-list-alt"></i> Failed Test Cases Details
                                        </h4>
                                        <div class="error-list-container">"""
                    
                    for j, error in enumerate(detailed_errors):
                        html_content += f"""
                                            <div class="error-item-card">
                                                <div class="error-item-header">
                                                    <span class="error-item-number">{j+1}</span>
                                                    <span class="error-item-title">{error['test_case']}</span>
                                                </div>
                                                <div class="error-item-message">{error['error_message']}</div>
                                            </div>"""
                    
                    html_content += """
                                        </div>
                                    </div>
                                </td>
                            </tr>"""
                else:
                    # Regular row for failed tests without detailed errors
                    html_content += f"""
                            <tr>
                                <td><strong>{test['test_name']}</strong></td>
                                <td><span class="status-badge {status_class}">{test['status']}</span></td>
                                <td>{test['test_cases']}</td>
                                <td>{test['tests']}</td>
                                <td><span style="color: var(--success-color); font-weight: 600;">{test['passes']}</span></td>
                                <td><span style="color: var(--error-color); font-weight: 600;">{test['fails']}</span></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <span style="font-weight: 600;">{test['success_rate']:.1f}%</span>
                                        <div style="width: 60px; height: 6px; background: #e5e7eb; border-radius: 3px; overflow: hidden;">
                                            <div style="width: {test['success_rate']:.1f}%; height: 100%; background: var(--success-color); transition: width 0.3s ease;"></div>
                                        </div>
                                    </div>
                                </td>
                                <td><small style="color: var(--text-secondary);">No specific error details available</small></td>
                            </tr>"""
            else:
                # Regular row for passed tests
                html_content += f"""
                            <tr>
                                <td><strong>{test['test_name']}</strong></td>
                                <td><span class="status-badge {status_class}">{test['status']}</span></td>
                                <td>{test['test_cases']}</td>
                                <td>{test['tests']}</td>
                                <td><span style="color: var(--success-color); font-weight: 600;">{test['passes']}</span></td>
                                <td><span style="color: var(--error-color); font-weight: 600;">{test['fails']}</span></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <span style="font-weight: 600;">{test['success_rate']:.1f}%</span>
                                        <div style="width: 60px; height: 6px; background: #e5e7eb; border-radius: 3px; overflow: hidden;">
                                            <div style="width: {test['success_rate']:.1f}%; height: 100%; background: var(--success-color); transition: width 0.3s ease;"></div>
                                        </div>
                                    </div>
                                </td>
                                <td><span style="color: var(--success-color);">? No errors</span></td>
                            </tr>"""
    
    if not squish_results:
        html_content += """
                            <tr>
                                <td colspan="8" class="empty-state">
                                    <i class="fas fa-inbox"></i>
                                    <p>No Squish test results found</p>
                                </td>
                            </tr>"""

    html_content += f"""
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><i class="fas fa-chart-line"></i> Dashboard generated by Advanced Log Analyzer | <strong>{summary_stats['total_files']}</strong> files processed</p>
        </div>
    </div>

    <script>
        // Modern Chart.js configuration with smaller, elegant charts
        const chartDefaults = {{
            responsive: true,
            maintainAspectRatio: false,
            plugins: {{
                legend: {{
                    position: 'bottom',
                    labels: {{
                        padding: 15,
                        font: {{
                            family: 'Inter',
                            size: 12,
                            weight: '500'
                        }},
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }}
                }},
                tooltip: {{
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    cornerRadius: 8,
                    displayColors: false,
                    titleFont: {{
                        family: 'Inter',
                        weight: '600'
                    }},
                    bodyFont: {{
                        family: 'Inter'
                    }}
                }}
            }}
        }};

        // Results Overview Doughnut Chart
        const resultsCtx = document.getElementById('resultsChart').getContext('2d');
        new Chart(resultsCtx, {{
            type: 'doughnut',
            data: {{
                labels: ['Passed', 'Failed'],
                datasets: [{{
                    data: [{status_data['passes']}, {status_data['failures']}],
                    backgroundColor: [
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(239, 68, 68, 0.8)'
                    ],
                    borderColor: [
                        'rgb(16, 185, 129)',
                        'rgb(239, 68, 68)'
                    ],
                    borderWidth: 2,
                    hoverOffset: 8,
                    cutout: '60%'
                }}]
            }},
            options: {{
                ...chartDefaults,
                plugins: {{
                    ...chartDefaults.plugins,
                    legend: {{
                        ...chartDefaults.plugins.legend,
                        display: true
                    }}
                }}
            }}
        }});

        // Test Type Distribution Bar Chart
        const typeCtx = document.getElementById('typeChart').getContext('2d');
        new Chart(typeCtx, {{
            type: 'bar',
            data: {{
                labels: ['Regular Tests', 'Squish Tests'],
                datasets: [{{
                    label: 'Number of Tests',
                    data: [{test_type_data['Regular Tests']}, {test_type_data['Squish Tests']}],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(147, 51, 234, 0.8)'
                    ],
                    borderColor: [
                        'rgb(59, 130, 246)',
                        'rgb(147, 51, 234)'
                    ],
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false,
                }}]
            }},
            options: {{
                ...chartDefaults,
                plugins: {{
                    ...chartDefaults.plugins,
                    legend: {{
                        display: false
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        ticks: {{
                            stepSize: 1,
                            font: {{
                                family: 'Inter',
                                size: 11
                            }},
                            color: '#6b7280'
                        }},
                        grid: {{
                            color: 'rgba(229, 231, 235, 0.5)'
                        }}
                    }},
                    x: {{
                        ticks: {{
                            font: {{
                                family: 'Inter',
                                size: 11,
                                weight: '500'
                            }},
                            color: '#374151'
                        }},
                        grid: {{
                            display: false
                        }}
                    }}
                }}
            }}
        }});
        
        // Toggle row expansion for error details
        function toggleRowExpansion(id) {{
            const detailsRow = document.getElementById(id + '-details');
            const expandIcon = document.getElementById(id + '-icon');
            const mainRow = expandIcon.closest('tr');
            
            if (detailsRow.style.display === 'none' || detailsRow.style.display === '') {{
                detailsRow.style.display = 'table-row';
                expandIcon.classList.add('expanded');
                mainRow.classList.add('expanded');
            }} else {{
                detailsRow.style.display = 'none';
                expandIcon.classList.remove('expanded');
                mainRow.classList.remove('expanded');
            }}
        }}
        
        // Make function globally available
        window.toggleRowExpansion = toggleRowExpansion;
        
        // Add smooth animations
        document.querySelectorAll('.stat-card').forEach((card, index) => {{
            card.style.animationDelay = `${{index * 0.1}}s`;
            card.style.animation = 'fadeInUp 0.6s ease-out forwards';
        }});
        
        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {{
                from {{
                    opacity: 0;
                    transform: translateY(30px);
                }}
                to {{
                    opacity: 1;
                    transform: translateY(0);
                }}
            }}
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>"""

    # Save HTML report
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(html_content)

    print(f"? Ultra-modern HTML report generated: {output_file}")
    print(f"?? Report saved to: {os.path.abspath(output_file)}")
    return html_content

if __name__ == "__main__":
    # You can change the directory path here if needed
    log_dir = "/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration"
    
    # For testing locally, you can use current directory
    if not os.path.exists(log_dir):
        log_dir = "."
        print(f"Using current directory: {os.path.abspath(log_dir)}")
    
    # Debug: List all files in the directory
    print(f"Files in directory {log_dir}:")
    try:
        all_files = os.listdir(log_dir)
        log_files = [f for f in all_files if f.endswith('.log')]
        print(f"  Total files: {len(all_files)}")
        print(f"  Log files: {len(log_files)}")
        for log_file in log_files[:10]:  # Show first 10 log files
            print(f"    - {log_file}")
        if len(log_files) > 10:
            print(f"    ... and {len(log_files) - 10} more")
    except Exception as e:
        print(f"Error listing files: {e}")
    
    analyzer = LogAnalyzer(log_dir)
    summary_stats, test_results, squish_results = analyzer.analyze_logs()
    
    if summary_stats:
        print(f"\nSummary Statistics:")
        print(f"- Total Files: {summary_stats['total_files']}")
        print(f"- Total Passes: {summary_stats['total_passes']}")
        print(f"- Total Failures: {summary_stats['total_failures']}")
        print(f"- Success Rate: {summary_stats['success_rate']:.1f}%")

        # Generate HTML report
        try:
            generate_html_report(summary_stats, test_results, squish_results)
            print("\n✅ HTML report generated successfully!")
            print("📊 Report saved to: /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/test_report.html")
        except Exception as e:
            print(f"\n❌ Error generating HTML report: {e}")
            # Fallback to current directory
            try:
                generate_html_report(summary_stats, test_results, squish_results, "test_report.html")
                print("✅ HTML report generated successfully in current directory!")
                print("📊 Open 'test_report.html' in your browser to view the report.")
            except Exception as e2:
                print(f"❌ Failed to generate report even in current directory: {e2}")
    else:
        print("No log files found or analysis failed.")
