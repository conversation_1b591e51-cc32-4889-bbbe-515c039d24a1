USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 07:33:28.817 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3019

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 43152 ms
Current Time : 01-07-2027 07:34:12.005 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Patient details and image count found as expected for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 10:04:12.378 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 42715 ms
Current Time : 01-07-2027 10:04:55.128 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Patient details and image count found as expected for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 10:36:03.454 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3059

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 39155 ms
Current Time : 01-07-2027 10:36:42.646 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Unexpected Patient information found for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 10:45:02.284 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 2980

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 17472 ms
Current Time : 01-07-2027 10:45:19.793 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Unexpected Patient information found for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 11:05:24.183 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3047

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 11:15:07.940 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3053

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 42859 ms
Current Time : 01-07-2027 11:15:50.835 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Patient details and image count found as expected for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 11:22:05.651 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 49 ms

Squish Server is already running with pid 3035

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 6066 ms
Current Time : 01-07-2027 11:22:11.774 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Unexpected Patient information found for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 11:29:23.684 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 39 ms

Squish Server is already running with pid 3067

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 4929 ms
Current Time : 01-07-2027 11:29:28.659 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Unexpected Patient information found for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 11:38:21.465 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 33 ms

Squish Server is already running with pid 3049

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 4953 ms
Current Time : 01-07-2027 11:38:26.460 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Unexpected Patient information found for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 11:49:04.139 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 42761 ms
Current Time : 01-07-2027 11:49:46.938 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Patient details and image count found as expected for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 12:21:18.990 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 42735 ms
Current Time : 01-07-2027 12:22:01.762 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Patient details and image count found as expected for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2027 13:07:03.642 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 42779 ms
Current Time : 01-07-2027 13:07:46.458 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Patient details and image count found as expected for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A025 ***************



Checking for Sigmoid Test Pattern patient details and image count
Current Time : 01-07-2025 13:48:50.917 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_IMAGES
Time taken to execute script 42679 ms
Current Time : 01-07-2025 13:49:33.633 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A025_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Patient details and image count found as expected for Sigmoid Test Pattern
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A025 ***************

Test Result : SUCCESS
