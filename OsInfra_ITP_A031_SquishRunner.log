
**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_PDM_Restart_Application
Scenario Name - PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO
TestCase Start Time - 01-07-2027 10:13:25.689 AM
**********************************************************************************

2027-07-01T10:13:25	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30060014
2027-07-01T10:13:25	START_TEST_CASE	Start 'tst_PDM_Restart_Application'	Test 'tst_PDM_Restart_Application' started (tst_PDM_Restart_Application)
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: Start Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:13: Start Scenario	Perform Restart Application from PDM and click confirmation No
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:15: Start Step	Given PDM is UP
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_10:13:31.PNG	
2027-07-01T10:13:31	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Squish_Wrapper/Wrapper.py:713: Script Error	RuntimeError: Screenshot taken from the desktop is invalid (Perhaps the AUT has quit/crashed unexpectedly?)
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:15: End Step	Given PDM is UP
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:16: Skip Step	When I clicked on Power Icon
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:17: Skip Step	And I select Restart application from System power drop down menu
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:18: Skip Step	And Clicked on Restart application confirmation No button
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:19: Skip Step	Then Verify PDM is Up and Running
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:13: End Scenario	Perform Restart Application from PDM and click confirmation No
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: End Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2027-07-01T10:13:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:13:31	END_TEST_CASE	End 'tst_PDM_Restart_Application'	End of test 'tst_PDM_Restart_Application'
2027-07-01T10:13:31	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   0
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   0
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:13:31.847 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_PDM_Restart_Application
Scenario Name - PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO
TestCase Start Time - 01-07-2027 11:58:19.393 AM
**********************************************************************************

2027-07-01T11:58:19	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30110015
AUTID: 30110017
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30110017
RUNNERID: 30110018
AUTID: 30110019
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30110019
2027-07-01T11:58:19	START_TEST_CASE	Start 'tst_PDM_Restart_Application'	Test 'tst_PDM_Restart_Application' started (tst_PDM_Restart_Application)
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: Start Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:13: Start Scenario	Perform Restart Application from PDM and click confirmation No
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:15: Start Step	Given PDM is UP
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:58:25	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:15: End Step	Given PDM is UP
2027-07-01T11:58:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:16: Start Step	When I clicked on Power Icon
2027-07-01T11:58:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T11:58:26	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T11:58:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:16: End Step	When I clicked on Power Icon
2027-07-01T11:58:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:17: Start Step	And I select Restart application from System power drop down menu
2027-07-01T11:58:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Restart application from System power drop down menu	
2027-07-01T11:58:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Restart application	
2027-07-01T11:58:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Restart application is selected from the menu Power Icon	
2027-07-01T11:58:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:17: End Step	And I select Restart application from System power drop down menu
2027-07-01T11:58:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:18: Start Step	And Clicked on Restart application confirmation No button
2027-07-01T11:58:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Clicked on Restart application  confirmation Yes button	
2027-07-01T11:58:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Restart Confirmation No is performed	
2027-07-01T11:58:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Restart Confirmation No	
2027-07-01T11:58:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:18: End Step	And Clicked on Restart application confirmation No button
2027-07-01T11:58:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:19: Start Step	Then Verify PDM is Up and Running
2027-07-01T11:59:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:59:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:59:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:19: End Step	Then Verify PDM is Up and Running
2027-07-01T11:59:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:13: End Scenario	Perform Restart Application from PDM and click confirmation No
2027-07-01T11:59:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: End Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2027-07-01T11:59:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:59:14	END_TEST_CASE	End 'tst_PDM_Restart_Application'	End of test 'tst_PDM_Restart_Application'
2027-07-01T11:59:14	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:59:14.752 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_PDM_Restart_Application
Scenario Name - PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO
TestCase Start Time - 01-07-2027 12:30:33.337 PM
**********************************************************************************

2027-07-01T12:30:33	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 29730015
AUTID: 29730017
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730017
RUNNERID: 29730018
AUTID: 29730019
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730019
2027-07-01T12:30:33	START_TEST_CASE	Start 'tst_PDM_Restart_Application'	Test 'tst_PDM_Restart_Application' started (tst_PDM_Restart_Application)
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: Start Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:13: Start Scenario	Perform Restart Application from PDM and click confirmation No
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:15: Start Step	Given PDM is UP
2027-07-01T12:30:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:30:39	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:30:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:15: End Step	Given PDM is UP
2027-07-01T12:30:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:16: Start Step	When I clicked on Power Icon
2027-07-01T12:30:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T12:30:39	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T12:30:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:16: End Step	When I clicked on Power Icon
2027-07-01T12:30:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:17: Start Step	And I select Restart application from System power drop down menu
2027-07-01T12:30:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Restart application from System power drop down menu	
2027-07-01T12:30:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Restart application	
2027-07-01T12:30:42	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Restart application is selected from the menu Power Icon	
2027-07-01T12:30:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:17: End Step	And I select Restart application from System power drop down menu
2027-07-01T12:30:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:18: Start Step	And Clicked on Restart application confirmation No button
2027-07-01T12:30:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Clicked on Restart application  confirmation Yes button	
2027-07-01T12:30:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Restart Confirmation No is performed	
2027-07-01T12:30:42	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Restart Confirmation No	
2027-07-01T12:30:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:18: End Step	And Clicked on Restart application confirmation No button
2027-07-01T12:30:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:19: Start Step	Then Verify PDM is Up and Running
2027-07-01T12:31:27	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:31:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:31:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:19: End Step	Then Verify PDM is Up and Running
2027-07-01T12:31:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:13: End Scenario	Perform Restart Application from PDM and click confirmation No
2027-07-01T12:31:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: End Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2027-07-01T12:31:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:31:28	END_TEST_CASE	End 'tst_PDM_Restart_Application'	End of test 'tst_PDM_Restart_Application'
2027-07-01T12:31:28	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:31:28.715 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_PDM_Restart_Application
Scenario Name - PERFORM_RESTART_APPLICATION_FROM_PDM
TestCase Start Time - 01-07-2027 12:31:28.725 PM
**********************************************************************************

2027-07-01T12:31:28	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 29730020
AUTID: 29730022
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730022
RUNNERID: 29730023
AUTID: 29730024
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730024
2027-07-01T12:31:28	START_TEST_CASE	Start 'tst_PDM_Restart_Application'	Test 'tst_PDM_Restart_Application' started (tst_PDM_Restart_Application)
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: Start Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:4: Start Scenario	Perform Restart Application from PDM
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:6: Start Step	Given PDM is UP
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:31:34	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:6: End Step	Given PDM is UP
2027-07-01T12:31:34	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:7: Start Step	When I clicked on Power Icon
2027-07-01T12:31:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T12:31:35	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T12:31:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:7: End Step	When I clicked on Power Icon
2027-07-01T12:31:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:8: Start Step	And I select Restart application from System power drop down menu
2027-07-01T12:31:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Restart application from System power drop down menu	
2027-07-01T12:31:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Restart application	
2027-07-01T12:31:37	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Restart application is selected from the menu Power Icon	
2027-07-01T12:31:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:8: End Step	And I select Restart application from System power drop down menu
2027-07-01T12:31:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:9: Start Step	And Clicked on Restart application confirmation Yes button
2027-07-01T12:31:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Clicked on Restart application  confirmation Yes button	
2027-07-01T12:31:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Restart confirmation Yes is performed	
2027-07-01T12:31:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Restart confirmation Yes	
2027-07-01T12:32:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:9: End Step	And Clicked on Restart application confirmation Yes button
2027-07-01T12:32:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:10: Start Step	Then Verify PDM is Up and Running
2027-07-01T12:33:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:33:18	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:33:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:10: End Step	Then Verify PDM is Up and Running
2027-07-01T12:33:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:4: End Scenario	Perform Restart Application from PDM
2027-07-01T12:33:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: End Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2027-07-01T12:33:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:33:18	END_TEST_CASE	End 'tst_PDM_Restart_Application'	End of test 'tst_PDM_Restart_Application'
2027-07-01T12:33:19	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:33:19.220 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_PDM_Restart_Application
Scenario Name - PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO
TestCase Start Time - 01-07-2025 13:58:05.260 PM
**********************************************************************************

2025-07-01T13:58:05	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30820015
AUTID: 30820017
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820017
RUNNERID: 30820018
AUTID: 30820019
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820019
2025-07-01T13:58:05	START_TEST_CASE	Start 'tst_PDM_Restart_Application'	Test 'tst_PDM_Restart_Application' started (tst_PDM_Restart_Application)
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: Start Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:13: Start Scenario	Perform Restart Application from PDM and click confirmation No
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:15: Start Step	Given PDM is UP
2025-07-01T13:58:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:58:11	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:58:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:15: End Step	Given PDM is UP
2025-07-01T13:58:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:16: Start Step	When I clicked on Power Icon
2025-07-01T13:58:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2025-07-01T13:58:11	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2025-07-01T13:58:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:16: End Step	When I clicked on Power Icon
2025-07-01T13:58:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:17: Start Step	And I select Restart application from System power drop down menu
2025-07-01T13:58:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Restart application from System power drop down menu	
2025-07-01T13:58:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Restart application	
2025-07-01T13:58:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Restart application is selected from the menu Power Icon	
2025-07-01T13:58:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:17: End Step	And I select Restart application from System power drop down menu
2025-07-01T13:58:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:18: Start Step	And Clicked on Restart application confirmation No button
2025-07-01T13:58:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Clicked on Restart application  confirmation Yes button	
2025-07-01T13:58:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Restart Confirmation No is performed	
2025-07-01T13:58:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Restart Confirmation No	
2025-07-01T13:58:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:18: End Step	And Clicked on Restart application confirmation No button
2025-07-01T13:58:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:19: Start Step	Then Verify PDM is Up and Running
2025-07-01T13:58:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:59:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:59:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:19: End Step	Then Verify PDM is Up and Running
2025-07-01T13:59:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:13: End Scenario	Perform Restart Application from PDM and click confirmation No
2025-07-01T13:59:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: End Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2025-07-01T13:59:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:59:00	END_TEST_CASE	End 'tst_PDM_Restart_Application'	End of test 'tst_PDM_Restart_Application'
2025-07-01T13:59:00	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:59:00.727 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_PDM_Restart_Application
Scenario Name - PERFORM_RESTART_APPLICATION_FROM_PDM
TestCase Start Time - 01-07-2025 13:59:00.737 PM
**********************************************************************************

2025-07-01T13:59:00	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30820020
AUTID: 30820022
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820022
RUNNERID: 30820023
AUTID: 30820024
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820024
2025-07-01T13:59:00	START_TEST_CASE	Start 'tst_PDM_Restart_Application'	Test 'tst_PDM_Restart_Application' started (tst_PDM_Restart_Application)
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: Start Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:4: Start Scenario	Perform Restart Application from PDM
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:6: Start Step	Given PDM is UP
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:59:06	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:6: End Step	Given PDM is UP
2025-07-01T13:59:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:7: Start Step	When I clicked on Power Icon
2025-07-01T13:59:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2025-07-01T13:59:07	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2025-07-01T13:59:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:7: End Step	When I clicked on Power Icon
2025-07-01T13:59:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:8: Start Step	And I select Restart application from System power drop down menu
2025-07-01T13:59:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Restart application from System power drop down menu	
2025-07-01T13:59:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Restart application	
2025-07-01T13:59:09	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Restart application is selected from the menu Power Icon	
2025-07-01T13:59:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:8: End Step	And I select Restart application from System power drop down menu
2025-07-01T13:59:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:9: Start Step	And Clicked on Restart application confirmation Yes button
2025-07-01T13:59:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Clicked on Restart application  confirmation Yes button	
2025-07-01T13:59:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Restart confirmation Yes is performed	
2025-07-01T13:59:10	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Restart confirmation Yes	
2025-07-01T14:00:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:9: End Step	And Clicked on Restart application confirmation Yes button
2025-07-01T14:00:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:10: Start Step	Then Verify PDM is Up and Running
2025-07-01T14:00:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T14:00:50	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T14:00:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:10: End Step	Then Verify PDM is Up and Running
2025-07-01T14:00:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:4: End Scenario	Perform Restart Application from PDM
2025-07-01T14:00:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_PDM_Restart_Application/test.feature:1: End Feature	RESTART_APPLICATION : The purpose of this feature is to perform restart application from PDM
2025-07-01T14:00:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T14:00:50	END_TEST_CASE	End 'tst_PDM_Restart_Application'	End of test 'tst_PDM_Restart_Application'
2025-07-01T14:00:51	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 14:00:51.256 PM
**********************************************************************************


