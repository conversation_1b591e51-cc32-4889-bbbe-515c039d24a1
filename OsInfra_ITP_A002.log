USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 26 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 4 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 26 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 5 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 25 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 5 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 25 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 3 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 24 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 3 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 24 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 4 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 25 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 2 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 23 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 3 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 24 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 3 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 24 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 4 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 24 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 3 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 23 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 3 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 25 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 4 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 24 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 4 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 23 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 4 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 23 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 5 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A002 ***************


Checking for Platform rpms installation errors

Executing : bash -c grep -i "not installed" /export/home/<USER>/install_platform.log
Time taken to execute script 25 ms

Checking if all Platform RPMs are installed successfully

Executing : bash -c grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
Time taken to execute script 4 ms

Actual RPMs which Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,css-bluefish,css-commander,css-trawler,demoImage,DicomHostMgmt,edelivery,EDOC,GpuMonitor,Healthpage,i18n,icu_32bit,insite-connectivity-configuration,InSiteRSvPAgent-DGS_MAMMO_SENO_PRISTINA,IPM,ipp,jtar,Licences,log4j,MammoEDelivery,NewTabOverride,patternImages,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,Qt6,rapidxml,RCOC,RogueWave,RSvP,RSVPAgent_PythonModules,RTI,SII,softwaredownload,SUIFWatchDog,SWConfigurationManagement,testng,TOMCAT,xercesc,xmlrpc

Expected RPMs To be Installed Successfully : ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationError = grep -i "not installed" /export/home/<USER>/install_platform.log
InputCheckInstallationSuccess = grep -i "was installed successfully" /export/home/<USER>/install_platform.log | awk '{$1=$1};1'|cut -f1 -d " "
OutputCheckPlatformRpms = ace,AnonimizeTag,BackupRestoreFramework,BackupRestorePlugins,calypso,cipher,csd,demoImage,DicomHostMgmt,Healthpage,i18n,icu_32bit,IPM,ipp,jtar,Licences,log4j,NewTabOverride,PLTCore,PLTCSComm,PLTDDSComm,PLTEthComm,PLTInfra,PLTLogging,PLTService,PLTSnapshot,PLTSSAAuthentication,PLTSystemMonitoring,PLTUserNotification,rapidxml,RCOC,RogueWave,RSvP,RTI,SII,SUIFWatchDog,testng,TOMCAT,xercesc,xmlrpc
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

No errors found in Platform RPMs installation
All Platform RPMs are installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A002 ***************

Test Result : SUCCESS
