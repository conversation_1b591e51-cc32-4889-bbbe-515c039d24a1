USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A014 ***************

Executing command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
Std Input Stream : 0
Std Input Stream : 1
Std Error Stream : Pseudo-terminal will not be allocated because stdin is not a terminal.
Std Error Stream : Warning: Permanently added '127.0.0.1' (ED25519) to the list of known hosts.
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : THIS IS A PROTECTED MEDICAL DEVICE.
Std Error Stream :  This device, including all related equipment, is provided only for authorized use. This device may be monitored for all lawful purposes, including to ensure that their use is authorized, for management of the device, to facilitate protection against unauthorized access, and to verify security procedures, survivability and operational security. Monitoring includes active attacks by authorized personnel and their entities to test or verify the security of the device. During monitoring, information may be examined, recorded, copied and used for authorized purposes. Uses of this device, authorized or unauthorized, constitutes consent to monitoring of this device. Unauthorized use may subject you to criminal prosecution. Evidence of any such unauthorized use collected during monitoring may be used for administrative, criminal or other adverse action.
Std Error Stream : 
Std Error Stream : ***********************************************************************************************************************
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 13: /etc/init.d/vsftpd: No such file or directory
Std Error Stream : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh: line 19: ftp: command not found
Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToRunFtpLocalhost = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/java_components/Integration/runFtpLocalhost.sh swd Mws3.ng# | grep "Login successful."
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Executed  /etc/init.d/vsftpd start and 'ftp localhost' as root user successfuly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A014 ***************

Test Result : SUCCESS
