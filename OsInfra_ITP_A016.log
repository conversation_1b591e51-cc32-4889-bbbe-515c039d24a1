USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 24 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 24 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 23 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 23 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 26 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 24 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 24 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 23 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 24 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 24 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 23 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 23 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 24 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 26 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A016 ***************


Checking rmem_max value


Running command : 'cat /proc/sys/net/core/rmem_max'

Executing : bash -c cat /proc/sys/net/core/rmem_max
Time taken to execute script 25 ms

Expected output : '14643200'

Actual output : '14643200'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = cat /proc/sys/net/core/rmem_max
ExpectedOutput = 14643200
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A016 ***************

Test Result : SUCCESS
