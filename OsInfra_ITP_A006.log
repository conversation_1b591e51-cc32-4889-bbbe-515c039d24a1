USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 07:29:42.375 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3019

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 6199 ms
Current Time : 01-07-2027 07:29:48.610 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Clicked on Browser tab,Browser page failed to display
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Browser tab,Browser page failed to display 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 09:53:00.751 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 31 ms

Squish Server is already running with pid 3044

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 6129 ms
Current Time : 01-07-2027 09:53:06.918 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Clicked on Browser tab,Browser page failed to display
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Browser tab,Browser page failed to display 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 10:00:25.818 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 31 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 6118 ms
Current Time : 01-07-2027 10:00:31.973 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Clicked on Browser tab,Browser page failed to display
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Browser tab,Browser page failed to display 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 10:32:17.682 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3059

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 6125 ms
Current Time : 01-07-2027 10:32:23.843 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Clicked on Browser tab,Browser page failed to display
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Browser tab,Browser page failed to display 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 10:41:16.524 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 2980

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 6124 ms
Current Time : 01-07-2027 10:41:22.684 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Clicked on Browser tab,Browser page failed to display
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Browser tab,Browser page failed to display 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 11:01:37.990 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3047

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 6122 ms
Current Time : 01-07-2027 11:01:44.148 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Clicked on Browser tab,Browser page failed to display
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Browser tab,Browser page failed to display 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 11:10:31.561 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3053

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 36870 ms
Current Time : 01-07-2027 11:11:08.468 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2027 11:11:08.469 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3053

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 16617 ms
Current Time : 01-07-2027 11:11:25.095 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Worklist tab,Worklist page displayed successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page displayed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 11:20:41.494 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3035

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 17548 ms
Current Time : 01-07-2027 11:20:59.077 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Clicked on Browser tab,Browser page failed to display
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Browser tab,Browser page failed to display 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 11:26:49.221 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3067

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 36614 ms
Current Time : 01-07-2027 11:27:25.871 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2027 11:27:25.872 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3067

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 16625 ms
Current Time : 01-07-2027 11:27:42.505 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Worklist tab,Worklist page displayed successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page displayed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 11:35:28.650 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3049

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 36885 ms
Current Time : 01-07-2027 11:36:05.572 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2027 11:36:05.573 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3049

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 16810 ms
Current Time : 01-07-2027 11:36:22.391 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Worklist tab,Worklist page displayed successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page displayed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 11:44:24.620 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 37092 ms
Current Time : 01-07-2027 11:45:01.749 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2027 11:45:01.750 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 18059 ms
Current Time : 01-07-2027 11:45:19.817 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Clicked on Worklist tab,Worklist page failed to display
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page failed to display 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 12:04:15.783 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3018

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 36790 ms
Current Time : 01-07-2027 12:04:52.608 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2027 12:04:52.609 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3018

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 16805 ms
Current Time : 01-07-2027 12:05:09.421 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Worklist tab,Worklist page displayed successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page displayed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 12:11:22.597 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 36788 ms
Current Time : 01-07-2027 12:11:59.422 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2027 12:11:59.423 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 7435 ms
Current Time : 01-07-2027 12:12:06.866 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Clicked on Worklist tab,Worklist page failed to display
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page failed to display 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 12:16:42.954 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 36905 ms
Current Time : 01-07-2027 12:17:19.896 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2027 12:17:19.901 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 16472 ms
Current Time : 01-07-2027 12:17:36.381 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Worklist tab,Worklist page displayed successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page displayed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 12:54:54.837 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3036

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 36855 ms
Current Time : 01-07-2027 12:55:31.729 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2027 12:55:31.730 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3036

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 16769 ms
Current Time : 01-07-2027 12:55:48.507 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Worklist tab,Worklist page displayed successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page displayed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2027 13:02:26.537 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 37382 ms
Current Time : 01-07-2027 13:03:03.954 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2027 13:03:03.955 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 16611 ms
Current Time : 01-07-2027 13:03:20.574 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Worklist tab,Worklist page displayed successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page displayed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A006 ***************

Clicking on Browser tab,Browser page should be displayed
Current Time : 01-07-2025 13:44:12.872 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_BROWSER_PAGE_DISPLAYED
Time taken to execute script 36873 ms
Current Time : 01-07-2025 13:44:49.783 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Browser tab,Browser page displayed successfully 
Clicking on Worklist tab,Worklist page should be displayed
Current Time : 01-07-2025 13:44:49.788 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Worklist --tags @VERIFY_WORKLIST_PAGE_DISPLAYED
Time taken to execute script 16632 ms
Current Time : 01-07-2025 13:45:06.427 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A006_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicking on Worklist tab,Worklist page displayed successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Browser:VERIFY_BROWSER_PAGE_DISPLAYED,tst_Worklist:VERIFY_WORKLIST_PAGE_DISPLAYED
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicking on Browser tab,Browser page displayed successfully
Clicking on Worklist tab,Worklist page displayed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A006 ***************

Test Result : SUCCESS
