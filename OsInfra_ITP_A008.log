USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A008 ***************

Executing command : bash -c nusm status | grep "Event Router" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Event Router is Running is success
Executing command : bash -c nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Media Manager is Running is success
Executing command : bash -c nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DBExpress is Running is success
Executing command : bash -c nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check Image Importer is Running is success 
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
Executing command : bash -c nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
Failed to execute the command
Execution Status to check DICOM Java Server is Running is success
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToCheckEventRouterIsRunning = bash,-c,nusm status | grep "Event Router" | grep "Running." >/dev/null ;
InputToCheckEventRouterIsNotRunning = bash,-c,nusm status | grep "Event Router" | grep "Not Running." >/dev/null ;
InputToCheckMediaManagerIsRunning = bash,-c,nusm status | grep "Media Manager" | grep "Running." >/dev/null ;
InputToCheckMediaManagerIsNotRunning = bash,-c,nusm status | grep "Media Manager" | grep "Not Running." >/dev/null ;
InputToCheckDBExpressIsRunning = bash,-c,nusm status | grep "DBExpress" | grep "Running." >/dev/null ;
InputToCheckDBExpressIsNotRunning = bash,-c,nusm status | grep "DBExpress" | grep "Not Running." >/dev/null ;
InputToCheckServiceProviderIsRunning = bash,-c,nusm status | grep "Service Provider" | grep "Running." >/dev/null ;
InputToCheckServiceProviderIsNotRunning = bash,-c,nusm status | grep "Service Provider" | grep "Not Running." >/dev/null ;
InputToCheckImageImporterIsRunning = bash,-c,nusm status | grep "Image Importer" | grep "Running." >/dev/null ;
InputToCheckImageImporterIsNotRunning = bash,-c,nusm status | grep "Image Importer" | grep "Not Running." >/dev/null ;
InputToCheckDICOMJavaServerIsRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Running." >/dev/null ;
InputToCheckDICOMJavaServerIsNotRunning = bash,-c,nusm status | grep "DICOM Java Server" | grep "Not Running." >/dev/null ;
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Execution Status to check Event Router is Running is success
Execution Status to check Media Manager is Running is success
Execution Status to check DBExpress is Running is success
Execution Status to check Image Importer is Running is success
Execution Status to check DICOM Java Server is Running is success
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A008 ***************

Test Result : SUCCESS
