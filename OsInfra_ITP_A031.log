USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A031 ***************


Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "Shutdown" | cut -f1-3 -d ':'
Time taken to execute script 24 ms
Current Time : 2027-07-01,10:13:25

Calculating time difference

Start Time : 2027-07-01,10:10:05
End time : 2027-07-01,10:13:25

Time difference in seconds : 200

Expected Time Difference In Seconds : 400

Actual Time Difference In Seconds : 200
system Shutdown was done successfully when Clicked Yes on Confirmation pop up
Clicking on Restart Application, Confirmation pop up appears ,Click on 'No'
Current Time : 01-07-2027 10:13:25.689 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3006

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_PDM_Restart_Application --tags @PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO
Time taken to execute script 6150 ms
Current Time : 01-07-2027 10:13:31.847 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A031_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click on Restart Application and to Click No on Confirmation pop up
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputAcceptableLastRestartTimeDiffInSecs = 300
InputAcceptableLastRebootTimeDiffInSecs = 400
InputSquishTestCaseSequence = tst_PDM_Restart_Application:PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO,tst_PDM_Restart_Application:PERFORM_RESTART_APPLICATION_FROM_PDM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

system Shutdown was done successfully when Clicked Yes on Confirmation pop up
 Failed to Click on Restart Application and to Click No on Confirmation pop up 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A031 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A031 ***************


Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "Shutdown" | cut -f1-3 -d ':'
Time taken to execute script 24 ms
Current Time : 2027-07-01,11:58:19

Calculating time difference

Start Time : 2027-07-01,11:54:56
End time : 2027-07-01,11:58:19

Time difference in seconds : 203

Expected Time Difference In Seconds : 400

Actual Time Difference In Seconds : 203
system Shutdown was done successfully when Clicked Yes on Confirmation pop up
Clicking on Restart Application, Confirmation pop up appears ,Click on 'No'
Current Time : 01-07-2027 11:58:19.393 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3011

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_PDM_Restart_Application --tags @PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO
Time taken to execute script 55351 ms
Current Time : 01-07-2027 11:59:14.752 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A031_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Restart Application, Confirmation pop up appeared ,Clicked on No successfuly 

Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "All Mammo applications are restarting" | cut -f1-3 -d ':'
Time taken to execute script 4 ms
Current Time : 2027-07-01,11:59:14

Calculating time difference

Start Time : 2027-07-01,11:47:35
End time : 2027-07-01,11:59:14

Time difference in seconds : 699

Expected Time Difference In Seconds : 300

Actual Time Difference In Seconds : 699

Unexpected Restart time difference
Application Restart was not done when Clicked 'No' on Confirmation pop up
Clicking on Restart Application from power dropdown menu
Current Time : 01-07-2027 11:59:14.763 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 11 ms

Squish Server is already running with pid 3011

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_PDM_Restart_Application --tags @PERFORM_RESTART_APPLICATION_FROM_PDM
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A031 ***************


Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "Shutdown" | cut -f1-3 -d ':'
Time taken to execute script 24 ms
Current Time : 2027-07-01,12:30:33

Calculating time difference

Start Time : 2027-07-01,12:27:11
End time : 2027-07-01,12:30:33

Time difference in seconds : 202

Expected Time Difference In Seconds : 400

Actual Time Difference In Seconds : 202
system Shutdown was done successfully when Clicked Yes on Confirmation pop up
Clicking on Restart Application, Confirmation pop up appears ,Click on 'No'
Current Time : 01-07-2027 12:30:33.337 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 2973

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_PDM_Restart_Application --tags @PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO
Time taken to execute script 55371 ms
Current Time : 01-07-2027 12:31:28.715 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A031_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Restart Application, Confirmation pop up appeared ,Clicked on No successfuly 

Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "All Mammo applications are restarting" | cut -f1-3 -d ':'
Time taken to execute script 4 ms
Current Time : 2027-07-01,12:31:28

Calculating time difference

Start Time : 2027-07-01,12:19:50
End time : 2027-07-01,12:31:28

Time difference in seconds : 698

Expected Time Difference In Seconds : 300

Actual Time Difference In Seconds : 698

Unexpected Restart time difference
Application Restart was not done when Clicked 'No' on Confirmation pop up
Clicking on Restart Application from power dropdown menu
Current Time : 01-07-2027 12:31:28.725 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 2973

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_PDM_Restart_Application --tags @PERFORM_RESTART_APPLICATION_FROM_PDM
Time taken to execute script 110485 ms
Current Time : 01-07-2027 12:33:19.220 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A031_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Restart Application, Confirmation pop up appeared ,Clicked on Yes successfuly 

Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "All Mammo applications are restarting" | cut -f1-3 -d ':'
Time taken to execute script 7 ms
Current Time : 2027-07-01,12:33:19

Calculating time difference

Start Time : 2027-07-01,12:31:37
End time : 2027-07-01,12:33:19

Time difference in seconds : 102

Expected Time Difference In Seconds : 300

Actual Time Difference In Seconds : 102
Application Restart was done successfully when Clicked 'Yes' on Confirmation pop up
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputAcceptableLastRestartTimeDiffInSecs = 300
InputAcceptableLastRebootTimeDiffInSecs = 400
InputSquishTestCaseSequence = tst_PDM_Restart_Application:PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO,tst_PDM_Restart_Application:PERFORM_RESTART_APPLICATION_FROM_PDM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

system Shutdown was done successfully when Clicked Yes on Confirmation pop up
Clicked on Restart Application, Confirmation pop up appeared ,Clicked on No successfuly 
 Application Restart was not done when Clicked 'No' on Confirmation pop up
Clicked on Restart Application, Confirmation pop up appeared ,Clicked on Yes successfuly 
 Application Restart was done successfully when Clicked 'Yes' on Confirmation pop up
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A031 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A031 ***************


Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "Shutdown" | cut -f1-3 -d ':'
Time taken to execute script 26 ms
Current Time : 2025-07-01,13:58:05

Calculating time difference

Start Time : 2025-07-01,13:54:43
End time : 2025-07-01,13:58:05

Time difference in seconds : 202

Expected Time Difference In Seconds : 400

Actual Time Difference In Seconds : 202
system Shutdown was done successfully when Clicked Yes on Confirmation pop up
Clicking on Restart Application, Confirmation pop up appears ,Click on 'No'
Current Time : 01-07-2025 13:58:05.260 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 12 ms

Squish Server is already running with pid 3082

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_PDM_Restart_Application --tags @PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO
Time taken to execute script 55455 ms
Current Time : 01-07-2025 13:59:00.727 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A031_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Restart Application, Confirmation pop up appeared ,Clicked on No successfuly 

Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "All Mammo applications are restarting" | cut -f1-3 -d ':'
Time taken to execute script 4 ms
Current Time : 2025-07-01,13:59:00

Calculating time difference

Start Time : 2025-07-01,13:47:22
End time : 2025-07-01,13:59:00

Time difference in seconds : 698

Expected Time Difference In Seconds : 300

Actual Time Difference In Seconds : 698

Unexpected Restart time difference
Application Restart was not done when Clicked 'No' on Confirmation pop up
Clicking on Restart Application from power dropdown menu
Current Time : 01-07-2025 13:59:00.737 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3082

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_PDM_Restart_Application --tags @PERFORM_RESTART_APPLICATION_FROM_PDM
Time taken to execute script 110512 ms
Current Time : 01-07-2025 14:00:51.256 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A031_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Restart Application, Confirmation pop up appeared ,Clicked on Yes successfuly 

Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "All Mammo applications are restarting" | cut -f1-3 -d ':'
Time taken to execute script 8 ms
Current Time : 2025-07-01,14:00:51

Calculating time difference

Start Time : 2025-07-01,13:59:09
End time : 2025-07-01,14:00:51

Time difference in seconds : 102

Expected Time Difference In Seconds : 300

Actual Time Difference In Seconds : 102
Application Restart was done successfully when Clicked 'Yes' on Confirmation pop up
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputAcceptableLastRestartTimeDiffInSecs = 300
InputAcceptableLastRebootTimeDiffInSecs = 400
InputSquishTestCaseSequence = tst_PDM_Restart_Application:PERFORM_RESTART_APPLICATION_FROM_PDM_CLICK_NO,tst_PDM_Restart_Application:PERFORM_RESTART_APPLICATION_FROM_PDM
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

system Shutdown was done successfully when Clicked Yes on Confirmation pop up
Clicked on Restart Application, Confirmation pop up appeared ,Clicked on No successfuly 
 Application Restart was not done when Clicked 'No' on Confirmation pop up
Clicked on Restart Application, Confirmation pop up appeared ,Clicked on Yes successfuly 
 Application Restart was done successfully when Clicked 'Yes' on Confirmation pop up
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A031 ***************

Test Result : SUCCESS
