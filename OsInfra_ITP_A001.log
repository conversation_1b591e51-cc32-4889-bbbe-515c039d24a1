USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A001 ***************


Checking if Platform is installed successfully
Executing command : bash -c grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
Std Input Stream :  Platform Installed Successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationSuccess = grep -i "Platform Installed Successfully" /export/home/<USER>/install_platform.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Platform is installed successfully
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A001 ***************

Test Result : SUCCESS
