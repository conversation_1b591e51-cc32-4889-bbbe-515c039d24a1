USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 07:29:50.484 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 32 ms

Squish Server is already running with pid 3019

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 6154 ms
Current Time : 01-07-2027 07:29:56.677 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 09:53:09.156 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3044

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 6125 ms
Current Time : 01-07-2027 09:53:15.317 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 10:00:34.270 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 34 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 6135 ms
Current Time : 01-07-2027 10:00:40.447 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 10:32:25.619 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3059

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 6131 ms
Current Time : 01-07-2027 10:32:31.786 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 10:41:24.489 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 2980

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 6170 ms
Current Time : 01-07-2027 10:41:30.696 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 11:01:46.003 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3047

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 6177 ms
Current Time : 01-07-2027 11:01:52.215 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 11:11:27.551 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3053

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8735 ms
Current Time : 01-07-2027 11:11:36.321 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 11:21:01.699 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3035

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8767 ms
Current Time : 01-07-2027 11:21:10.502 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 11:27:44.708 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3067

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8748 ms
Current Time : 01-07-2027 11:27:53.493 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 11:36:24.775 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3049

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8784 ms
Current Time : 01-07-2027 11:36:33.595 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 11:45:22.298 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8852 ms
Current Time : 01-07-2027 11:45:31.184 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 12:05:11.721 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3018

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8213 ms
Current Time : 01-07-2027 12:05:19.970 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click On power dropdown menu and verify Lock Screen,Log Out,Restart Application and Shutdown link is displayed 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 12:17:38.915 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8698 ms
Current Time : 01-07-2027 12:17:47.649 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 12:55:50.897 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3036

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8771 ms
Current Time : 01-07-2027 12:55:59.705 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2027 13:03:22.937 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8767 ms
Current Time : 01-07-2027 13:03:31.741 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A010 ***************

Clicking on power dropdown icon
Current Time : 01-07-2025 13:45:08.544 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Power_Icon --tags @POWERICON_SUBMENU_VISIBLE
Time taken to execute script 8719 ms
Current Time : 01-07-2025 13:45:17.299 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A010_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_Power_Icon:POWERICON_SUBMENU_VISIBLE
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked On power dropdown menu and verified Lock Screen,Log Out,Restart Application and Shutdown link is displayed successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A010 ***************

Test Result : SUCCESS
