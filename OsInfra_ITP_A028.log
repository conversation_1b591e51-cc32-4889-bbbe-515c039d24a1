USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A028 ***************

Click On power dropdown menu->Lock screen
Current Time : 01-07-2027 07:35:48.665 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3019

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOCK_SCREEN
Time taken to execute script 8804 ms
Current Time : 01-07-2027 07:35:57.505 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click On power dropdown menu->Lock screen
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOCK_SCREEN,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Failed to Click On power dropdown menu->Lock screen 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A028 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A028 ***************

Click On power dropdown menu->Lock screen
Current Time : 01-07-2027 10:07:48.394 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOCK_SCREEN
Time taken to execute script 15212 ms
Current Time : 01-07-2027 10:08:03.641 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu->Lock screen successfuly
Log in as clinical user ,verify PDM displays
Current Time : 01-07-2027 10:08:03.646 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 68926 ms
Current Time : 01-07-2027 10:09:12.580 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged in as clinical user ,verified PDM displays successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOCK_SCREEN,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked On power dropdown menu->Lock screen successfuly 
Logged in as clinical user ,verified PDM displays successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A028 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A028 ***************

Click On power dropdown menu->Lock screen
Current Time : 01-07-2027 10:45:46.582 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 2980

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOCK_SCREEN
Time taken to execute script 8847 ms
Current Time : 01-07-2027 10:45:55.465 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click On power dropdown menu->Lock screen
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOCK_SCREEN,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Failed to Click On power dropdown menu->Lock screen 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A028 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A028 ***************

Click On power dropdown menu->Lock screen
Current Time : 01-07-2027 11:52:39.391 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOCK_SCREEN
Time taken to execute script 15209 ms
Current Time : 01-07-2027 11:52:54.636 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu->Lock screen successfuly
Log in as clinical user ,verify PDM displays
Current Time : 01-07-2027 11:52:54.640 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 9 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 68865 ms
Current Time : 01-07-2027 11:54:03.514 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged in as clinical user ,verified PDM displays successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOCK_SCREEN,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked On power dropdown menu->Lock screen successfuly 
Logged in as clinical user ,verified PDM displays successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A028 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A028 ***************

Click On power dropdown menu->Lock screen
Current Time : 01-07-2027 12:24:53.970 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOCK_SCREEN
Time taken to execute script 15178 ms
Current Time : 01-07-2027 12:25:09.183 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu->Lock screen successfuly
Log in as clinical user ,verify PDM displays
Current Time : 01-07-2027 12:25:09.183 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 68875 ms
Current Time : 01-07-2027 12:26:18.067 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged in as clinical user ,verified PDM displays successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOCK_SCREEN,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked On power dropdown menu->Lock screen successfuly 
Logged in as clinical user ,verified PDM displays successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A028 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A028 ***************

Click On power dropdown menu->Lock screen
Current Time : 01-07-2027 13:09:41.757 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOCK_SCREEN
Time taken to execute script 6128 ms
Current Time : 01-07-2027 13:09:47.921 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click On power dropdown menu->Lock screen
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOCK_SCREEN,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Failed to Click On power dropdown menu->Lock screen 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A028 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A028 ***************

Click On power dropdown menu->Lock screen
Current Time : 01-07-2025 13:52:26.504 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOCK_SCREEN
Time taken to execute script 15111 ms
Current Time : 01-07-2025 13:52:41.653 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked On power dropdown menu->Lock screen successfuly
Log in as clinical user ,verify PDM displays
Current Time : 01-07-2025 13:52:41.653 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 68856 ms
Current Time : 01-07-2025 13:53:50.517 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A028_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged in as clinical user ,verified PDM displays successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOCK_SCREEN,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked On power dropdown menu->Lock screen successfuly 
Logged in as clinical user ,verified PDM displays successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A028 ***************

Test Result : SUCCESS
