USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A036 ***************



Verifying that logfiles are not archived if total log size if less than 600MB
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A001
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A001 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/oldFiles/*
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/systemLogFiles/*
Std Input Stream : 
Std Input Stream : Checking total log size before running the script
Std Input Stream : 
Std Input Stream :  commandToGetLogSize :find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:du -cm /export/logfiles/senovision/pos/|grep total|cut -f1
Std Input Stream : Logs Total Size : 96MB (Less Than 800MB)
Std Input Stream : 
Std Input Stream : Running Log Cleaner
Std Input Stream : Executing:/export/home/<USER>/senovision/scripts/logCleaner.sh boot
Std Input Stream : Executing:stat /export/logfiles/senovision/LastlogCleaner.log | grep Modify |cut -c9-27
Std Input Stream : Log Cleaner Last Running Time : 2027-07-01 10:19:27
Std Input Stream : 
Std Input Stream : Checking for new archives in /export/logfiles/senovision/oldFiles/
Std Input Stream : Executing:find /export/logfiles/senovision/oldFiles/ -name "trace*log.tar.gz" -newermt "2027-07-01 10:19:27" -ls  | awk '{print $11}'
Std Input Stream : New Log Archives : 
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeAfterLogCleanerExecutionInSecs = 0
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Logs total size is less than 800MB
Std Input Stream : No new archive created in /export/logfiles/senovision/oldFiles/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A001 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunLogCleaner = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A001
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Log archive not created
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A036 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A036 ***************



Verifying that logfiles are not archived if total log size if less than 600MB
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A001
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A001 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/oldFiles/*
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/systemLogFiles/*
Std Input Stream : 
Std Input Stream : Checking total log size before running the script
Std Input Stream : 
Std Input Stream :  commandToGetLogSize :find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:du -cm /export/logfiles/senovision/pos/|grep total|cut -f1
Std Input Stream : Logs Total Size : 100MB (Less Than 800MB)
Std Input Stream : 
Std Input Stream : Running Log Cleaner
Std Input Stream : Executing:/export/home/<USER>/senovision/scripts/logCleaner.sh boot
Std Input Stream : Executing:stat /export/logfiles/senovision/LastlogCleaner.log | grep Modify |cut -c9-27
Std Input Stream : Log Cleaner Last Running Time : 2027-07-01 12:39:15
Std Input Stream : 
Std Input Stream : Checking for new archives in /export/logfiles/senovision/oldFiles/
Std Input Stream : Executing:find /export/logfiles/senovision/oldFiles/ -name "trace*log.tar.gz" -newermt "2027-07-01 12:39:15" -ls  | awk '{print $11}'
Std Input Stream : New Log Archives : 
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeAfterLogCleanerExecutionInSecs = 0
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Logs total size is less than 800MB
Std Input Stream : No new archive created in /export/logfiles/senovision/oldFiles/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A001 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunLogCleaner = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A001
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Log archive not created
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A036 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A036 ***************



Verifying that logfiles are not archived if total log size if less than 600MB
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A001
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A001 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/oldFiles/*
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/systemLogFiles/*
Std Input Stream : 
Std Input Stream : Checking total log size before running the script
Std Input Stream : 
Std Input Stream :  commandToGetLogSize :find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:find /export/logfiles/senovision/ -type f -name 'trace.*.log*' ! -name "trace.UserEvents.log" ! -path "/export/logfiles/senovision/systemLogFiles/*" -exec du -cm {} + | grep total |cut -f1
Std Input Stream : Executing:du -cm /export/logfiles/senovision/pos/|grep total|cut -f1
Std Input Stream : Logs Total Size : 47MB (Less Than 800MB)
Std Input Stream : 
Std Input Stream : Running Log Cleaner
Std Input Stream : Executing:/export/home/<USER>/senovision/scripts/logCleaner.sh boot
Std Input Stream : Executing:stat /export/logfiles/senovision/LastlogCleaner.log | grep Modify |cut -c9-27
Std Input Stream : Log Cleaner Last Running Time : 2025-07-01 14:06:49
Std Input Stream : 
Std Input Stream : Checking for new archives in /export/logfiles/senovision/oldFiles/
Std Input Stream : Executing:find /export/logfiles/senovision/oldFiles/ -name "trace*log.tar.gz" -newermt "2025-07-01 14:06:49" -ls  | awk '{print $11}'
Std Input Stream : New Log Archives : 
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeAfterLogCleanerExecutionInSecs = 0
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Logs total size is less than 800MB
Std Input Stream : No new archive created in /export/logfiles/senovision/oldFiles/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A001 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunLogCleaner = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A001
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Log archive not created
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A036 ***************

Test Result : SUCCESS
