<testsuites tests="" failures="" time="" timestamp="2025-07-04T07:16:07.000476+0000">
<testsuite name="ClassA" tests="" failures="" time="" timestamp="2025-07-04T07:16:07.000476+0000">
<properties>
<property name="OSVersion" value="Axis : OS : NG-SLES-15.50-1.4"/>
<property name="SWPlatform" value="Axis : SWPlatform : PLATFORM-9.50.32"/>
<property name="SwApps" value="Axis:SwApps:SWAPPS-9.50.28"/>
<property name="AcqSvcVersion" value="Axis:Swacqsvc:ACQSVC-9.50.17"/>
</properties>
<testcase name="openSUIF" classname="com.ge.hc.mammo.service.automatedtests.zion.SUIFUICommon" time="10.564" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkAOPConfigurationSNAlinkIsPresentForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="36.847" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="validateEContrastHDOnInHomePageForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.OptionManagement" time="6.297" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkLinearLUTPresentInSNAONConfigurationForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="15.622" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkeContrastSNAlinkIsPresentForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="16.479" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="check3DProcessingSNAlinkIsPresentForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="16.651" >
<error message="check3DProcessingSNAlinkIsPresentForClassA Failed">excuiteSuifeMethod(com.ge.hc.mammo.service.automatedtests.sats.SuifTest): Test Failed: 3D Processing link is not available</error>
<system-out>TEST_FAILED</system-out>
</testcase>
<testcase name="checkCESMProcessingSNAlinkIsPresentForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="16.879" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="verifyMessgaeAfterEnableMultiplePushWhenItIsAlreadyEnabled" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="17.022" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="verifyMessgaeAfterDisableMultiplePushWhenItIsAlreadyDisabled" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="17.374" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="check_IfFilterIsAbsentForClass_A" classname="com.ge.hc.mammo.service.automatedtests.testcases.LogViewer" time="14.873" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="check_LogViewerHasCorrect_ColumnsForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.LogViewer" time="15.14" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkDateandTimePageCorrectlyDisplayedForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="18.284" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkIfCorrectHomepageDisclaimerDisplayedForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.HomePage" time="9.301" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkIfErrorCodeAreHyperLinked_Absent_ForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.LogViewer" time="14.982" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="Class_A_testHomePageTitleAvailable" classname="com.ge.hc.mammo.service.automatedtests.testcases.HomePage" time="30.173" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkCRMNumberDisplayedInHomePageForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.HomePage" time="15.067" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkLinkAboutIsPresentInUtilitiesWorkstationSNAForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.OSS_License_About" time="15.844" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkNewlyCreatedLinkIsDisplayedONAboutPageForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.OSS_License_About" time="17.201" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkViewingLevelFunctionalityOfLogViewerForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.LogViewer" time="15.168" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkSetStartExamErrorLog" classname="com.ge.hc.mammo.service.automatedtests.testcases.LogViewer" time="6.274" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddAndPingSuccessPrinterHostReachable" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemotePrinters" time="30.578" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testGetRemoteHost" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteHostScriptTest" time="22.298" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testEditRemoteHost" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteHostScriptTest" time="39.707" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testDeleteRemoteHost" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteHostScriptTest" time="29.039" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddArchivePACSSuccessUsingScript" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteHostScriptTest" time="20.44" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddArchivePACSSuccessWithStorageCommitmentUsingScript" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteHostScriptTest" time="27.697" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddAndPingSuccessPrinterHostNotReachable" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemotePrinters" time="33.908" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddArchivePACSSuccess" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="31.167" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="deleteReviewArchive" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="7.392" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddReviewWorkstationSuccess" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="21.608" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testDiagnosticsFeatureNotAvailableforClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.CommonTest" time="12.188" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testDisableMultiplePush" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="17.185" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testFirewallInClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.FirewallTest" time="40.725" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testEnableMultiplePush" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="17.394" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testInvalidInputsWhileAddingArchivePACS" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="42.189" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddArchivePACSSuccessWithEnableExportToCto" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="30.419" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddAndEditUpdateSuccessArchivePACSWithExportToCto" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="34.5" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddAndEditUpdateSuccessReviewWSWithExportToCto" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="34.544" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddEditArchivePACSWithVolumeTransferSyntaxesAndExportAsCTO" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="40.163" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddEditReviewWorkStationWithVolumeTransferSyntaxesAndExportAsCTO" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="40.119" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="verifyDefaultVolumeTranferSyntaxesListInRemoteHostsOnExportAsCTOEnabled" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="16.115" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testInvalidInputsWhileAddingPrinter" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemotePrinters" time="32.722" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testSystemAetParametersSuccess" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="21.728" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testSystemXrayTubeSNSuccess" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="22.709" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testSystemXrayTubeSNInvalidInputs" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="17.208" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkXRayTubeSNIsUpdatedbyScript" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="17.144" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkXRayTubeSNIsGettedbyScript" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="23.059" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkXRayTubeSNShowInvalidbyScript" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="0.072" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testThreeBackUps" classname="com.ge.hc.mammo.service.automatedtests.testcases.BackupRestore" time="66.995" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testTwoBackupsSameFileName" classname="com.ge.hc.mammo.service.automatedtests.testcases.BackupRestore" time="50.679" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testBackupAndRestoreSystemSettingsSuccess" classname="com.ge.hc.mammo.service.automatedtests.testcases.BackupRestore" time="49.846" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddAndDeletePrinter" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemotePrinters" time="17.393" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testInvalidInputsWhileAddingReviewWorkstation" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="36.036" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddEditArchivePACSWithVolumeTransferSyntaxes" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="41.35" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="testAddEditReviewWorkStationWithVolumeTransferSyntaxes" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="42.527" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="verifyDefaultVolumeTranferSyntaxesListInRemoteHosts" classname="com.ge.hc.mammo.service.automatedtests.testcases.RemoteReviewArchive" time="12.387" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="verifyCalibrationTitleAndTestDeatilsTablePresentForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.Calibration" time="14.91" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkSelectedLanguageAndKeyBoardLayoutisDisplayedCorrectlyOnHomePageForClassA" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="923.938" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkInstructionsTitlePresentOnRecoveryPage" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="17.067" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkInstructionsTitlePresentOnCDROMEjectPage" classname="com.ge.hc.mammo.service.automatedtests.testcases.SystemConfiguration" time="17.11" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkGeneratorOptionPresentOnSNAPageAndReplacementPage" classname="com.ge.hc.mammo.service.automatedtests.testcases.Replacement" time="15.691" >
<system-out>TEST_PASSED</system-out>
</testcase>
<testcase name="checkTnTOptionPresentOnSNASubMenuAndReplacementPage" classname="com.ge.hc.mammo.service.automatedtests.testcases.Replacement"