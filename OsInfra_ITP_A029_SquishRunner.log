
**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN_CLICK_NO
TestCase Start Time - 01-07-2027 07:35:57.561 AM
**********************************************************************************

2027-07-01T07:35:57	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30190063
AUTID: 30190065
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190065
2027-07-01T07:35:57	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: Start Scenario	Verify Shutdown and click confirmation No
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: Start Step	Given PDM is UP
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T07:36:03	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: End Step	Given PDM is UP
2027-07-01T07:36:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: Start Step	When I clicked on Power Icon
2027-07-01T07:36:05	WARNING   	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:35: ERROR :button click object Power Icon could not be found	
2027-07-01T07:36:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_07:36:05.PNG	
2027-07-01T07:36:05	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:58: Script Error	RuntimeError: TEST FAILURE :Failed to perform button click on Power Icon:
2027-07-01T07:36:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: End Step	When I clicked on Power Icon
2027-07-01T07:36:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: Skip Step	And Select Shutdown from System power drop down menu
2027-07-01T07:36:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: Skip Step	And Clicked on Shutdown confirmation No button
2027-07-01T07:36:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: End Scenario	Verify Shutdown and click confirmation No
2027-07-01T07:36:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T07:36:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T07:36:05	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2027-07-01T07:36:06	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   1
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   1
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   1
*******************************************************
ERROR:root:button click object Power Icon could not be found

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 07:36:06.461 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN_CLICK_NO
TestCase Start Time - 01-07-2027 10:09:12.641 AM
**********************************************************************************

2027-07-01T10:09:12	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 28010089
AUTID: 28010091
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010091
2027-07-01T10:09:12	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: Start Scenario	Verify Shutdown and click confirmation No
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: Start Step	Given PDM is UP
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:09:18	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: End Step	Given PDM is UP
2027-07-01T10:09:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: Start Step	When I clicked on Power Icon
2027-07-01T10:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T10:09:19	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T10:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: End Step	When I clicked on Power Icon
2027-07-01T10:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: Start Step	And Select Shutdown from System power drop down menu
2027-07-01T10:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select Shutdown from System power drop down menu	
2027-07-01T10:09:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Shutdown	
2027-07-01T10:09:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Shutdown is selected from the menu Power Icon	
2027-07-01T10:09:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: End Step	And Select Shutdown from System power drop down menu
2027-07-01T10:09:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: Start Step	And Clicked on Shutdown confirmation No button
2027-07-01T10:09:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Shutdown confirmation No button	
2027-07-01T10:09:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object System shutdown confirmation NO button is performed	
2027-07-01T10:09:24	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on System shutdown confirmation NO button	
2027-07-01T10:09:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: End Step	And Clicked on Shutdown confirmation No button
2027-07-01T10:09:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: End Scenario	Verify Shutdown and click confirmation No
2027-07-01T10:09:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T10:09:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:09:24	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2027-07-01T10:09:24	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   4
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   4
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:09:24.662 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN
TestCase Start Time - 01-07-2027 10:09:54.678 AM
**********************************************************************************

2027-07-01T10:09:54	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 28010092
AUTID: 28010094
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010094
2027-07-01T10:09:54	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:8: Start Scenario	Verify Shutdown
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:10: Start Step	Given PDM is UP
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:10:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:10: End Step	Given PDM is UP
2027-07-01T10:10:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:11: Start Step	When I clicked on Power Icon
2027-07-01T10:10:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T10:10:01	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T10:10:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:11: End Step	When I clicked on Power Icon
2027-07-01T10:10:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:12: Start Step	And Select Shutdown from System power drop down menu
2027-07-01T10:10:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select Shutdown from System power drop down menu	
2027-07-01T10:10:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Shutdown	
2027-07-01T10:10:03	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Shutdown is selected from the menu Power Icon	
2027-07-01T10:10:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:12: End Step	And Select Shutdown from System power drop down menu
2027-07-01T10:10:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:13: Start Step	And Clicked on Shutdown confirmation Yes button
2027-07-01T10:10:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Shutdown confirmation Yes button	
2027-07-01T10:10:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object System shutdown confirmation YES button is performed	
2027-07-01T10:10:06	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on System shutdown confirmation YES button	
2027-07-01T10:10:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:13: End Step	And Clicked on Shutdown confirmation Yes button
2027-07-01T10:10:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:8: End Scenario	Verify Shutdown
2027-07-01T10:10:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T10:10:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:10:06	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2027-07-01T10:10:06	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   4
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   4
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:10:06.754 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN_CLICK_NO
TestCase Start Time - 01-07-2027 10:45:55.531 AM
**********************************************************************************

2027-07-01T10:45:55	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 29800042
AUTID: 29800044
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29800044
2027-07-01T10:45:55	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: Start Scenario	Verify Shutdown and click confirmation No
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: Start Step	Given PDM is UP
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:46:01	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: End Step	Given PDM is UP
2027-07-01T10:46:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: Start Step	When I clicked on Power Icon
2027-07-01T10:46:03	WARNING   	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:35: ERROR :button click object Power Icon could not be found	
2027-07-01T10:46:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_10:46:03.PNG	
2027-07-01T10:46:03	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:58: Script Error	RuntimeError: TEST FAILURE :Failed to perform button click on Power Icon:
2027-07-01T10:46:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: End Step	When I clicked on Power Icon
2027-07-01T10:46:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: Skip Step	And Select Shutdown from System power drop down menu
2027-07-01T10:46:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: Skip Step	And Clicked on Shutdown confirmation No button
2027-07-01T10:46:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: End Scenario	Verify Shutdown and click confirmation No
2027-07-01T10:46:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T10:46:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:46:03	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2027-07-01T10:46:04	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   1
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   1
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   1
*******************************************************
ERROR:root:button click object Power Icon could not be found

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:46:04.409 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN_CLICK_NO
TestCase Start Time - 01-07-2027 11:54:03.574 AM
**********************************************************************************

2027-07-01T11:54:03	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30460117
AUTID: 30460119
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460119
2027-07-01T11:54:03	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: Start Scenario	Verify Shutdown and click confirmation No
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: Start Step	Given PDM is UP
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:54:09	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: End Step	Given PDM is UP
2027-07-01T11:54:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: Start Step	When I clicked on Power Icon
2027-07-01T11:54:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T11:54:10	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T11:54:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: End Step	When I clicked on Power Icon
2027-07-01T11:54:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: Start Step	And Select Shutdown from System power drop down menu
2027-07-01T11:54:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select Shutdown from System power drop down menu	
2027-07-01T11:54:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Shutdown	
2027-07-01T11:54:12	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Shutdown is selected from the menu Power Icon	
2027-07-01T11:54:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: End Step	And Select Shutdown from System power drop down menu
2027-07-01T11:54:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: Start Step	And Clicked on Shutdown confirmation No button
2027-07-01T11:54:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Shutdown confirmation No button	
2027-07-01T11:54:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object System shutdown confirmation NO button is performed	
2027-07-01T11:54:15	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on System shutdown confirmation NO button	
2027-07-01T11:54:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: End Step	And Clicked on Shutdown confirmation No button
2027-07-01T11:54:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: End Scenario	Verify Shutdown and click confirmation No
2027-07-01T11:54:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T11:54:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:54:15	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2027-07-01T11:54:15	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   4
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   4
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:54:15.631 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN
TestCase Start Time - 01-07-2027 11:54:45.653 AM
**********************************************************************************

2027-07-01T11:54:45	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30460120
AUTID: 30460122
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460122
2027-07-01T11:54:45	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:8: Start Scenario	Verify Shutdown
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:10: Start Step	Given PDM is UP
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:54:51	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:10: End Step	Given PDM is UP
2027-07-01T11:54:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:11: Start Step	When I clicked on Power Icon
2027-07-01T11:54:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T11:54:52	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T11:54:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:11: End Step	When I clicked on Power Icon
2027-07-01T11:54:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:12: Start Step	And Select Shutdown from System power drop down menu
2027-07-01T11:54:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select Shutdown from System power drop down menu	
2027-07-01T11:54:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Shutdown	
2027-07-01T11:54:54	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Shutdown is selected from the menu Power Icon	
2027-07-01T11:54:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:12: End Step	And Select Shutdown from System power drop down menu
2027-07-01T11:54:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:13: Start Step	And Clicked on Shutdown confirmation Yes button
2027-07-01T11:54:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Shutdown confirmation Yes button	
2027-07-01T11:54:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object System shutdown confirmation YES button is performed	
2027-07-01T11:54:57	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on System shutdown confirmation YES button	
2027-07-01T11:54:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:13: End Step	And Clicked on Shutdown confirmation Yes button
2027-07-01T11:54:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:8: End Scenario	Verify Shutdown
2027-07-01T11:54:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T11:54:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:54:57	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2027-07-01T11:54:57	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   4
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   4
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:54:57.732 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN_CLICK_NO
TestCase Start Time - 01-07-2027 12:26:18.126 PM
**********************************************************************************

2027-07-01T12:26:18	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130117
AUTID: 30130119
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130119
2027-07-01T12:26:18	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: Start Scenario	Verify Shutdown and click confirmation No
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: Start Step	Given PDM is UP
2027-07-01T12:26:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:26:24	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:26:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: End Step	Given PDM is UP
2027-07-01T12:26:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: Start Step	When I clicked on Power Icon
2027-07-01T12:26:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T12:26:24	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T12:26:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: End Step	When I clicked on Power Icon
2027-07-01T12:26:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: Start Step	And Select Shutdown from System power drop down menu
2027-07-01T12:26:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select Shutdown from System power drop down menu	
2027-07-01T12:26:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Shutdown	
2027-07-01T12:26:26	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Shutdown is selected from the menu Power Icon	
2027-07-01T12:26:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: End Step	And Select Shutdown from System power drop down menu
2027-07-01T12:26:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: Start Step	And Clicked on Shutdown confirmation No button
2027-07-01T12:26:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Shutdown confirmation No button	
2027-07-01T12:26:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object System shutdown confirmation NO button is performed	
2027-07-01T12:26:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on System shutdown confirmation NO button	
2027-07-01T12:26:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: End Step	And Clicked on Shutdown confirmation No button
2027-07-01T12:26:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: End Scenario	Verify Shutdown and click confirmation No
2027-07-01T12:26:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T12:26:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:26:29	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2027-07-01T12:26:30	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   4
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   4
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:26:30.085 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN
TestCase Start Time - 01-07-2027 12:27:00.103 PM
**********************************************************************************

2027-07-01T12:27:00	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130120
AUTID: 30130122
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130122
2027-07-01T12:27:00	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:8: Start Scenario	Verify Shutdown
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:10: Start Step	Given PDM is UP
2027-07-01T12:27:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:27:06	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:27:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:10: End Step	Given PDM is UP
2027-07-01T12:27:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:11: Start Step	When I clicked on Power Icon
2027-07-01T12:27:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T12:27:06	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T12:27:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:11: End Step	When I clicked on Power Icon
2027-07-01T12:27:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:12: Start Step	And Select Shutdown from System power drop down menu
2027-07-01T12:27:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select Shutdown from System power drop down menu	
2027-07-01T12:27:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Shutdown	
2027-07-01T12:27:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Shutdown is selected from the menu Power Icon	
2027-07-01T12:27:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:12: End Step	And Select Shutdown from System power drop down menu
2027-07-01T12:27:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:13: Start Step	And Clicked on Shutdown confirmation Yes button
2027-07-01T12:27:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Shutdown confirmation Yes button	
2027-07-01T12:27:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object System shutdown confirmation YES button is performed	
2027-07-01T12:27:11	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on System shutdown confirmation YES button	
2027-07-01T12:27:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:13: End Step	And Clicked on Shutdown confirmation Yes button
2027-07-01T12:27:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:8: End Scenario	Verify Shutdown
2027-07-01T12:27:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T12:27:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:27:11	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2027-07-01T12:27:12	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   4
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   4
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:27:12.034 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN_CLICK_NO
TestCase Start Time - 01-07-2027 13:09:47.980 PM
**********************************************************************************

2027-07-01T13:09:48	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30210097
AUTID: 30210099
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30210099
2027-07-01T13:09:48	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: Start Scenario	Verify Shutdown and click confirmation No
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: Start Step	Given PDM is UP
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T13:09:54	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: End Step	Given PDM is UP
2027-07-01T13:09:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: Start Step	When I clicked on Power Icon
2027-07-01T13:09:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T13:09:55	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T13:09:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: End Step	When I clicked on Power Icon
2027-07-01T13:09:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: Start Step	And Select Shutdown from System power drop down menu
2027-07-01T13:09:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select Shutdown from System power drop down menu	
2027-07-01T13:10:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_13:10:17.PNG	
2027-07-01T13:10:17	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:58: Script Error	RuntimeError: TEST FAILURE :failed to select Shutdown :
2027-07-01T13:10:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: End Step	And Select Shutdown from System power drop down menu
2027-07-01T13:10:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: Skip Step	And Clicked on Shutdown confirmation No button
2027-07-01T13:10:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: End Scenario	Verify Shutdown and click confirmation No
2027-07-01T13:10:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2027-07-01T13:10:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T13:10:17	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2027-07-01T13:10:18	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   2
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   2
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 13:10:18.488 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN_CLICK_NO
TestCase Start Time - 01-07-2025 13:53:50.575 PM
**********************************************************************************

2025-07-01T13:53:50	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130117
AUTID: 30130119
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130119
2025-07-01T13:53:50	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: Start Scenario	Verify Shutdown and click confirmation No
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: Start Step	Given PDM is UP
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:53:56	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:18: End Step	Given PDM is UP
2025-07-01T13:53:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: Start Step	When I clicked on Power Icon
2025-07-01T13:53:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2025-07-01T13:53:57	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2025-07-01T13:53:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:19: End Step	When I clicked on Power Icon
2025-07-01T13:53:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: Start Step	And Select Shutdown from System power drop down menu
2025-07-01T13:53:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select Shutdown from System power drop down menu	
2025-07-01T13:53:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Shutdown	
2025-07-01T13:53:59	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Shutdown is selected from the menu Power Icon	
2025-07-01T13:54:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:20: End Step	And Select Shutdown from System power drop down menu
2025-07-01T13:54:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: Start Step	And Clicked on Shutdown confirmation No button
2025-07-01T13:54:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Shutdown confirmation No button	
2025-07-01T13:54:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object System shutdown confirmation NO button is performed	
2025-07-01T13:54:02	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on System shutdown confirmation NO button	
2025-07-01T13:54:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:21: End Step	And Clicked on Shutdown confirmation No button
2025-07-01T13:54:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:16: End Scenario	Verify Shutdown and click confirmation No
2025-07-01T13:54:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2025-07-01T13:54:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:54:02	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2025-07-01T13:54:02	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   4
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   4
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:54:02.610 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_System_Shutdown
Scenario Name - SHUTDOWN
TestCase Start Time - 01-07-2025 13:54:32.630 PM
**********************************************************************************

2025-07-01T13:54:32	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130120
AUTID: 30130122
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130122
2025-07-01T13:54:32	START_TEST_CASE	Start 'tst_System_Shutdown'   	Test 'tst_System_Shutdown' started (tst_System_Shutdown)
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: Start Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:8: Start Scenario	Verify Shutdown
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:10: Start Step	Given PDM is UP
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:54:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:10: End Step	Given PDM is UP
2025-07-01T13:54:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:11: Start Step	When I clicked on Power Icon
2025-07-01T13:54:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2025-07-01T13:54:39	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2025-07-01T13:54:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:11: End Step	When I clicked on Power Icon
2025-07-01T13:54:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:12: Start Step	And Select Shutdown from System power drop down menu
2025-07-01T13:54:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Select Shutdown from System power drop down menu	
2025-07-01T13:54:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Shutdown	
2025-07-01T13:54:41	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Shutdown is selected from the menu Power Icon	
2025-07-01T13:54:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:12: End Step	And Select Shutdown from System power drop down menu
2025-07-01T13:54:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:13: Start Step	And Clicked on Shutdown confirmation Yes button
2025-07-01T13:54:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Shutdown confirmation Yes button	
2025-07-01T13:54:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object System shutdown confirmation YES button is performed	
2025-07-01T13:54:44	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on System shutdown confirmation YES button	
2025-07-01T13:54:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:13: End Step	And Clicked on Shutdown confirmation Yes button
2025-07-01T13:54:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:8: End Scenario	Verify Shutdown
2025-07-01T13:54:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_System_Shutdown/test.feature:5: End Feature	COMMON_SQUISH_UTILS : The purpose of this feature is to perform Common squish util functions
2025-07-01T13:54:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:54:44	END_TEST_CASE	End 'tst_System_Shutdown'     	End of test 'tst_System_Shutdown'
2025-07-01T13:54:44	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   4
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   4
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:54:44.644 PM
**********************************************************************************


