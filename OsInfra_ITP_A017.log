USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 24 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 24 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 25 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 26 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 23 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 26 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 24 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 26 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 24 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 23 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 26 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 25 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 24 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 25 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A017 ***************


Checking rmem_max value


Running command : 'grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf'

Executing : bash -c grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
Time taken to execute script 24 ms

Expected output : '1'

Actual output : '1'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRunCommand = grep -Po 'kernel.sysrq = \K[0-9]+' /etc/sysctl.conf
ExpectedOutput = 1
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Command executed and expected output found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A017 ***************

Test Result : SUCCESS
