USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A041 ***************



Running command : 'mount | grep -i cgroup'

Executing : bash -c mount | grep -i cgroup
Time taken to execute script 25 ms

ExpectedMountOutput : 'cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)'

Actual output : 'cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)'

Expected Mount output found


Running command : 'systemctl status Cgroups.service | grep Loaded'

Executing : bash -c systemctl status Cgroups.service | grep Loaded
Time taken to execute script 8 ms

ExpectedCgroupOutput : 'enabled'

Actual output : 'Loaded: loaded (/etc/systemd/system/Cgroups.service; enabled; preset: disabled)'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckCgroupMounted = mount | grep -i cgroup
OutputCheckCgroupMounted = cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)
InputCheckServiceEnabled = systemctl status Cgroups.service | grep Loaded
OutputCheckServiceEnabled = enabled
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A041 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A041 ***************



Running command : 'mount | grep -i cgroup'

Executing : bash -c mount | grep -i cgroup
Time taken to execute script 25 ms

ExpectedMountOutput : 'cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)'

Actual output : 'cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)'

Expected Mount output found


Running command : 'systemctl status Cgroups.service | grep Loaded'

Executing : bash -c systemctl status Cgroups.service | grep Loaded
Time taken to execute script 8 ms

ExpectedCgroupOutput : 'enabled'

Actual output : 'Loaded: loaded (/etc/systemd/system/Cgroups.service; enabled; preset: disabled)'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckCgroupMounted = mount | grep -i cgroup
OutputCheckCgroupMounted = cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)
InputCheckServiceEnabled = systemctl status Cgroups.service | grep Loaded
OutputCheckServiceEnabled = enabled
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A041 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A041 ***************



Running command : 'mount | grep -i cgroup'

Executing : bash -c mount | grep -i cgroup
Time taken to execute script 24 ms

ExpectedMountOutput : 'cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)'

Actual output : 'cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)'

Expected Mount output found


Running command : 'systemctl status Cgroups.service | grep Loaded'

Executing : bash -c systemctl status Cgroups.service | grep Loaded
Time taken to execute script 7 ms

ExpectedCgroupOutput : 'enabled'

Actual output : 'Loaded: loaded (/etc/systemd/system/Cgroups.service; enabled; preset: disabled)'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckCgroupMounted = mount | grep -i cgroup
OutputCheckCgroupMounted = cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)
InputCheckServiceEnabled = systemctl status Cgroups.service | grep Loaded
OutputCheckServiceEnabled = enabled
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A041 ***************

Test Result : SUCCESS
