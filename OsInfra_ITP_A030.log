USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A030 ***************


Waiting for 30 sec for Login page to be up..... 
Verfiying EA3 login screen after reboot

Verifying EA3 logon screen is visible or not
Current Time : 01-07-2027 10:12:05.093 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 27 ms

Squish Server is already running with pid 3006

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @VERIFY_EA3_LOGON_SCREEN_IS_VISIBLE
Time taken to execute script 11924 ms
Current Time : 01-07-2027 10:12:17.060 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A030_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Verified EA3 login screen successfully 
Performing Login as clinical user
Current Time : 01-07-2027 10:12:17.061 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 6 ms

Squish Server is already running with pid 3006

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 68531 ms
Current Time : 01-07-2027 10:13:25.599 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A030_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Log In Failed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:EA3LOGON_SCREEN_DISPLAYED_ON_TOP,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Verified EA3 login screen successfully 
Log In Failed 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A030 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A030 ***************


Waiting for 30 sec for Login page to be up..... 
Verfiying EA3 login screen after reboot

Verifying EA3 logon screen is visible or not
Current Time : 01-07-2027 11:56:58.048 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3011

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @VERIFY_EA3_LOGON_SCREEN_IS_VISIBLE
Time taken to execute script 12049 ms
Current Time : 01-07-2027 11:57:10.133 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A030_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Verified EA3 login screen successfully 
Performing Login as clinical user
Current Time : 01-07-2027 11:57:10.138 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3011

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 69158 ms
Current Time : 01-07-2027 11:58:19.303 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A030_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged In successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:EA3LOGON_SCREEN_DISPLAYED_ON_TOP,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Verified EA3 login screen successfully 
Logged In successfully 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A030 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A030 ***************


Waiting for 30 sec for Login page to be up..... 
Verfiying EA3 login screen after reboot

Verifying EA3 logon screen is visible or not
Current Time : 01-07-2027 12:29:12.200 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 2973

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @VERIFY_EA3_LOGON_SCREEN_IS_VISIBLE
Time taken to execute script 11949 ms
Current Time : 01-07-2027 12:29:24.187 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A030_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Verified EA3 login screen successfully 
Performing Login as clinical user
Current Time : 01-07-2027 12:29:24.192 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 2973

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 69043 ms
Current Time : 01-07-2027 12:30:33.242 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A030_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged In successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:EA3LOGON_SCREEN_DISPLAYED_ON_TOP,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Verified EA3 login screen successfully 
Logged In successfully 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A030 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A030 ***************


Waiting for 30 sec for Login page to be up..... 
Verfiying EA3 login screen after reboot

Verifying EA3 logon screen is visible or not
Current Time : 01-07-2025 13:56:44.076 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3082

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @VERIFY_EA3_LOGON_SCREEN_IS_VISIBLE
Time taken to execute script 11999 ms
Current Time : 01-07-2025 13:56:56.115 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A030_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Verified EA3 login screen successfully 
Performing Login as clinical user
Current Time : 01-07-2025 13:56:56.116 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3082

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 69041 ms
Current Time : 01-07-2025 13:58:05.164 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A030_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged In successfully 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:EA3LOGON_SCREEN_DISPLAYED_ON_TOP,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Verified EA3 login screen successfully 
Logged In successfully 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A030 ***************

Test Result : SUCCESS
