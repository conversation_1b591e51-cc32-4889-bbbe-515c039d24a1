
**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOCK_SCREEN
TestCase Start Time - 01-07-2027 07:35:48.665 AM
**********************************************************************************

2027-07-01T07:35:48	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30190060
AUTID: 30190062
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30190062
2027-07-01T07:35:48	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: Start Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: Start Step	Given PDM is UP
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T07:35:54	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: End Step	Given PDM is UP
2027-07-01T07:35:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: Start Step	When I clicked on Power Icon
2027-07-01T07:35:56	WARNING   	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:35: ERROR :button click object Power Icon could not be found	
2027-07-01T07:35:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_07:35:56.PNG	
2027-07-01T07:35:56	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:58: Script Error	RuntimeError: TEST FAILURE :Failed to perform button click on Power Icon:
2027-07-01T07:35:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: End Step	When I clicked on Power Icon
2027-07-01T07:35:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: Skip Step	And I select Lockscreen from System power drop down menu
2027-07-01T07:35:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: Skip Step	Then Logon is up and running
2027-07-01T07:35:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: Skip Step	Then EA3 Lock Screen is Visible
2027-07-01T07:35:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: End Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T07:35:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T07:35:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T07:35:56	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T07:35:57	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   1
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   1
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   1
*******************************************************
ERROR:root:button click object Power Icon could not be found

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 07:35:57.505 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOCK_SCREEN
TestCase Start Time - 01-07-2027 10:07:48.394 AM
**********************************************************************************

2027-07-01T10:07:48	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 28010073
AUTID: 28010075
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010075
RUNNERID: 28010076
AUTID: 28010077
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010077
RUNNERID: 28010078
AUTID: 28010079
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010079
2027-07-01T10:07:48	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: Start Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: Start Step	Given PDM is UP
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:07:54	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: End Step	Given PDM is UP
2027-07-01T10:07:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: Start Step	When I clicked on Power Icon
2027-07-01T10:07:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T10:07:55	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T10:07:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: End Step	When I clicked on Power Icon
2027-07-01T10:07:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: Start Step	And I select Lockscreen from System power drop down menu
2027-07-01T10:07:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Lockscreen from System power drop down menu	
2027-07-01T10:07:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Lock screen	
2027-07-01T10:07:57	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Lock screen is selected from the menu Power Icon	
2027-07-01T10:07:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: End Step	And I select Lockscreen from System power drop down menu
2027-07-01T10:07:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: Start Step	Then Logon is up and running
2027-07-01T10:08:02	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:08:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T10:08:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T10:08:03	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: End Step	Then Logon is up and running
2027-07-01T10:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: Start Step	Then EA3 Lock Screen is Visible
2027-07-01T10:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: EA3 Lock Screen is Visible	
2027-07-01T10:08:03	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :EA3 lock screen visible	
2027-07-01T10:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: End Step	Then EA3 Lock Screen is Visible
2027-07-01T10:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: End Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T10:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:08:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:08:03	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T10:08:03	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:08:03.641 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 10:08:03.646 AM
**********************************************************************************

2027-07-01T10:08:03	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 28010080
AUTID: 28010082
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010082
RUNNERID: 28010083
AUTID: 28010084
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010084
RUNNERID: 28010085
AUTID: 28010086
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010086
2027-07-01T10:08:03	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T10:08:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T10:08:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:08:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T10:08:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T10:08:15	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2027-07-01T10:08:15	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2027-07-01T10:08:15	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2027-07-01T10:08:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2027-07-01T10:08:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2027-07-01T10:08:16	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2027-07-01T10:08:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2027-07-01T10:08:26	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2027-07-01T10:08:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2027-07-01T10:08:31	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 28010087
AUTID: 28010088
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 28010088
2027-07-01T10:08:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T10:08:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2027-07-01T10:08:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2027-07-01T10:09:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:09:12	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:09:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2027-07-01T10:09:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T10:09:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:09:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:09:12	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T10:09:12	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:09:12.580 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOCK_SCREEN
TestCase Start Time - 01-07-2027 10:45:46.582 AM
**********************************************************************************

2027-07-01T10:45:46	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 29800039
AUTID: 29800041
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29800041
2027-07-01T10:45:46	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: Start Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: Start Step	Given PDM is UP
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:45:52	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: End Step	Given PDM is UP
2027-07-01T10:45:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: Start Step	When I clicked on Power Icon
2027-07-01T10:45:54	WARNING   	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:35: ERROR :button click object Power Icon could not be found	
2027-07-01T10:45:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_10:45:54.PNG	
2027-07-01T10:45:54	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:58: Script Error	RuntimeError: TEST FAILURE :Failed to perform button click on Power Icon:
2027-07-01T10:45:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: End Step	When I clicked on Power Icon
2027-07-01T10:45:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: Skip Step	And I select Lockscreen from System power drop down menu
2027-07-01T10:45:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: Skip Step	Then Logon is up and running
2027-07-01T10:45:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: Skip Step	Then EA3 Lock Screen is Visible
2027-07-01T10:45:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: End Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T10:45:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:45:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:45:54	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T10:45:55	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   1
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   1
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   1
*******************************************************
ERROR:root:button click object Power Icon could not be found

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:45:55.465 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOCK_SCREEN
TestCase Start Time - 01-07-2027 11:52:39.391 AM
**********************************************************************************

2027-07-01T11:52:39	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30460101
AUTID: 30460103
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460103
RUNNERID: 30460104
AUTID: 30460105
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460105
RUNNERID: 30460106
AUTID: 30460107
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460107
2027-07-01T11:52:39	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: Start Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: Start Step	Given PDM is UP
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:52:45	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: End Step	Given PDM is UP
2027-07-01T11:52:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: Start Step	When I clicked on Power Icon
2027-07-01T11:52:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T11:52:46	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T11:52:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: End Step	When I clicked on Power Icon
2027-07-01T11:52:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: Start Step	And I select Lockscreen from System power drop down menu
2027-07-01T11:52:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Lockscreen from System power drop down menu	
2027-07-01T11:52:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Lock screen	
2027-07-01T11:52:48	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Lock screen is selected from the menu Power Icon	
2027-07-01T11:52:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: End Step	And I select Lockscreen from System power drop down menu
2027-07-01T11:52:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: Start Step	Then Logon is up and running
2027-07-01T11:52:53	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T11:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T11:52:54	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:52:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: End Step	Then Logon is up and running
2027-07-01T11:52:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: Start Step	Then EA3 Lock Screen is Visible
2027-07-01T11:52:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: EA3 Lock Screen is Visible	
2027-07-01T11:52:54	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :EA3 lock screen visible	
2027-07-01T11:52:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: End Step	Then EA3 Lock Screen is Visible
2027-07-01T11:52:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: End Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T11:52:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:52:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:52:54	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T11:52:54	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:52:54.636 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 11:52:54.640 AM
**********************************************************************************

2027-07-01T11:52:54	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30460108
AUTID: 30460110
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460110
RUNNERID: 30460111
AUTID: 30460112
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460112
RUNNERID: 30460113
AUTID: 30460114
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460114
2027-07-01T11:52:54	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T11:53:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T11:53:05	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:53:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T11:53:05	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T11:53:06	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2027-07-01T11:53:06	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2027-07-01T11:53:06	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2027-07-01T11:53:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2027-07-01T11:53:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2027-07-01T11:53:07	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2027-07-01T11:53:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2027-07-01T11:53:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2027-07-01T11:53:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2027-07-01T11:53:22	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 30460115
AUTID: 30460116
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30460116
2027-07-01T11:53:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T11:53:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2027-07-01T11:53:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2027-07-01T11:54:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:54:03	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:54:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2027-07-01T11:54:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T11:54:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:54:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:54:03	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T11:54:03	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:54:03.514 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOCK_SCREEN
TestCase Start Time - 01-07-2027 12:24:53.970 PM
**********************************************************************************

2027-07-01T12:24:54	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130101
AUTID: 30130103
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130103
RUNNERID: 30130104
AUTID: 30130105
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130105
RUNNERID: 30130106
AUTID: 30130107
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130107
2027-07-01T12:24:54	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: Start Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: Start Step	Given PDM is UP
2027-07-01T12:24:59	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:25:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:25:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: End Step	Given PDM is UP
2027-07-01T12:25:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: Start Step	When I clicked on Power Icon
2027-07-01T12:25:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T12:25:00	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T12:25:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: End Step	When I clicked on Power Icon
2027-07-01T12:25:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: Start Step	And I select Lockscreen from System power drop down menu
2027-07-01T12:25:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Lockscreen from System power drop down menu	
2027-07-01T12:25:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Lock screen	
2027-07-01T12:25:02	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Lock screen is selected from the menu Power Icon	
2027-07-01T12:25:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: End Step	And I select Lockscreen from System power drop down menu
2027-07-01T12:25:02	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: Start Step	Then Logon is up and running
2027-07-01T12:25:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:25:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T12:25:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T12:25:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:25:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: End Step	Then Logon is up and running
2027-07-01T12:25:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: Start Step	Then EA3 Lock Screen is Visible
2027-07-01T12:25:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: EA3 Lock Screen is Visible	
2027-07-01T12:25:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :EA3 lock screen visible	
2027-07-01T12:25:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: End Step	Then EA3 Lock Screen is Visible
2027-07-01T12:25:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: End Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T12:25:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:25:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:25:08	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T12:25:09	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:25:09.183 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 12:25:09.183 PM
**********************************************************************************

2027-07-01T12:25:09	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130108
AUTID: 30130110
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130110
RUNNERID: 30130111
AUTID: 30130112
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130112
RUNNERID: 30130113
AUTID: 30130114
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130114
2027-07-01T12:25:09	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T12:25:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T12:25:20	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T12:25:20	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2027-07-01T12:25:20	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2027-07-01T12:25:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2027-07-01T12:25:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2027-07-01T12:25:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2027-07-01T12:25:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2027-07-01T12:25:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2027-07-01T12:25:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2027-07-01T12:25:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2027-07-01T12:25:31	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2027-07-01T12:25:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2027-07-01T12:25:37	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 30130115
AUTID: 30130116
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130116
2027-07-01T12:25:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T12:25:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2027-07-01T12:25:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2027-07-01T12:26:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:26:17	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:26:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2027-07-01T12:26:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T12:26:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:26:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:26:17	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T12:26:18	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:26:18.067 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOCK_SCREEN
TestCase Start Time - 01-07-2027 13:09:41.757 PM
**********************************************************************************

2027-07-01T13:09:41	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30210095
2027-07-01T13:09:41	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: Start Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: Start Step	Given PDM is UP
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_13:09:47.PNG	
2027-07-01T13:09:47	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Squish_Wrapper/Wrapper.py:713: Script Error	RuntimeError: Screenshot taken from the desktop is invalid (Perhaps the AUT has quit/crashed unexpectedly?)
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: End Step	Given PDM is UP
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: Skip Step	When I clicked on Power Icon
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: Skip Step	And I select Lockscreen from System power drop down menu
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: Skip Step	Then Logon is up and running
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: Skip Step	Then EA3 Lock Screen is Visible
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: End Scenario	Verify lockscreen when selected from System power drop menu
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T13:09:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T13:09:47	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T13:09:47	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   0
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   0
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 13:09:47.921 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOCK_SCREEN
TestCase Start Time - 01-07-2025 13:52:26.504 PM
**********************************************************************************

2025-07-01T13:52:26	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130101
AUTID: 30130103
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130103
RUNNERID: 30130104
AUTID: 30130105
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130105
RUNNERID: 30130106
AUTID: 30130107
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130107
2025-07-01T13:52:26	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: Start Scenario	Verify lockscreen when selected from System power drop menu
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: Start Step	Given PDM is UP
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:52:32	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:151: End Step	Given PDM is UP
2025-07-01T13:52:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: Start Step	When I clicked on Power Icon
2025-07-01T13:52:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2025-07-01T13:52:33	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2025-07-01T13:52:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:152: End Step	When I clicked on Power Icon
2025-07-01T13:52:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: Start Step	And I select Lockscreen from System power drop down menu
2025-07-01T13:52:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Lockscreen from System power drop down menu	
2025-07-01T13:52:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Lock screen	
2025-07-01T13:52:35	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Lock screen is selected from the menu Power Icon	
2025-07-01T13:52:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:153: End Step	And I select Lockscreen from System power drop down menu
2025-07-01T13:52:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: Start Step	Then Logon is up and running
2025-07-01T13:52:40	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:52:40	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2025-07-01T13:52:40	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2025-07-01T13:52:41	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:52:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:154: End Step	Then Logon is up and running
2025-07-01T13:52:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: Start Step	Then EA3 Lock Screen is Visible
2025-07-01T13:52:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: EA3 Lock Screen is Visible	
2025-07-01T13:52:41	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :EA3 lock screen visible	
2025-07-01T13:52:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:155: End Step	Then EA3 Lock Screen is Visible
2025-07-01T13:52:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:150: End Scenario	Verify lockscreen when selected from System power drop menu
2025-07-01T13:52:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:52:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:52:41	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2025-07-01T13:52:41	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:52:41.653 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2025 13:52:41.653 PM
**********************************************************************************

2025-07-01T13:52:41	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130108
AUTID: 30130110
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130110
RUNNERID: 30130111
AUTID: 30130112
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130112
RUNNERID: 30130113
AUTID: 30130114
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130114
2025-07-01T13:52:41	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2025-07-01T13:52:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2025-07-01T13:52:52	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:52:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2025-07-01T13:52:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2025-07-01T13:52:53	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2025-07-01T13:52:53	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2025-07-01T13:52:53	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2025-07-01T13:52:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2025-07-01T13:52:54	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2025-07-01T13:52:54	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2025-07-01T13:53:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2025-07-01T13:53:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2025-07-01T13:53:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2025-07-01T13:53:09	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 30130115
AUTID: 30130116
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30130116
2025-07-01T13:53:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2025-07-01T13:53:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2025-07-01T13:53:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2025-07-01T13:53:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:53:50	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:53:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2025-07-01T13:53:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2025-07-01T13:53:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:53:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:53:50	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2025-07-01T13:53:50	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:53:50.517 PM
**********************************************************************************


