USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 36 ms
Process ID of  umaiproxy process is sdc       5494     1  0 07:27 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5494     1  0 07:27 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5494     1  0 07:27 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 36 ms
Process ID of  umaiproxy process is sdc       5526     1  0 09:49 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5526     1  0 09:49 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5526     1  0 09:49 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 34 ms
Process ID of  umaiproxy process is sdc       5524     1  0 09:58 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5524     1  0 09:58 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5524     1  0 09:58 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 33 ms
Process ID of  umaiproxy process is sdc       5500     1  0 10:30 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5500     1  0 10:30 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5500     1  0 10:30 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 34 ms
Process ID of  umaiproxy process is sdc       5440     1  0 10:39 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5440     1  0 10:39 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5440     1  0 10:39 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 36 ms
Process ID of  umaiproxy process is sdc       5487     1  0 10:59 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5487     1  0 10:59 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5487     1  0 10:59 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 34 ms
Process ID of  umaiproxy process is sdc       5521     1  0 11:08 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5521     1  0 11:08 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5521     1  0 11:08 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 36 ms
Process ID of  umaiproxy process is sdc       5631     1  0 11:18 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5631     1  0 11:18 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5631     1  0 11:18 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 34 ms
Process ID of  umaiproxy process is sdc       5861     1  0 11:25 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5861     1  0 11:25 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5861     1  0 11:25 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 36 ms
Process ID of  umaiproxy process is sdc       5504     1  0 11:33 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5504     1  0 11:33 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5504     1  0 11:33 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 32 ms
Process ID of  umaiproxy process is sdc       5509     1  0 11:42 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5509     1  0 11:42 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5509     1  0 11:42 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 34 ms
Process ID of  umaiproxy process is sdc       5495     1  0 12:02 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5495     1  0 12:02 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5495     1  0 12:02 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 35 ms
Process ID of  umaiproxy process is sdc       5506     1  0 12:14 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5506     1  0 12:14 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5506     1  0 12:14 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 36 ms
Process ID of  umaiproxy process is sdc       5515     1  0 12:53 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5515     1  0 12:53 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5515     1  0 12:53 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 34 ms
Process ID of  umaiproxy process is sdc       5500     1  0 13:00 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5500     1  0 13:00 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5500     1  0 13:00 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A009 ***************


Executing : bash -c ps -aef | grep -v grep | grep "umaiproxy"
Time taken to execute script 33 ms
Process ID of  umaiproxy process is sdc       5508     1  0 13:42 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
Process ID of  umaiproxy process is sdc       5508     1  0 13:42 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToGetProcessID = bash,-c,ps -aef | grep -v grep | grep "umaiproxy"
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Process ID of  umaiproxy process is sdc       5508     1  0 13:42 ?        00:00:00 /export/home/<USER>/nuevo/bin/umaiproxy
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A009 ***************

Test Result : SUCCESS
