USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 19 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 9 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 11 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 17 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 18 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 9 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 7 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 13 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 10 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 15 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 7 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 4 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 14 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 17 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A022 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
Time taken to execute script 10 ms

Expected output : 'aaaaa'

Actual output : 'aaaaa'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/pos/postraces;logger -t postraces aaaaa;cat /export/home/<USER>/senovision/logfiles/pos/postraces|rev| cut -f1 -d":"|rev
ExpectedOutput = aaaaa
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A022 ***************

Test Result : SUCCESS
