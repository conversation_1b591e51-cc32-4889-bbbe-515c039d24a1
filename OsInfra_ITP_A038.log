USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A038 ***************


Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "grep 'Start: Cleanup of AXIS logs' /var/log/boot.log | tail -1 | cut -f1 -d' '"
Time taken to execute script 275 ms

Axis logs Cleanup Start time :2027-07-01,10:21:31

Executing : bash -c tac /export/home/<USER>/senovision/logfiles/LastlogCleaner.log | grep -i -m 1 "Starting logCleaner" | /bin/egrep -o "[0-9]{4}-[0-9]{2}-[0-9]{2},[0-9]{2}:[0-9]{2}:[0-9]{2}"
Time taken to execute script 5 ms

Log Cleaner Started time :2027-07-01,10:21:31

Calculating time difference

Start date : Thu Jul 01 10:21:31 UTC 2027
End date : Thu Jul 01 10:21:31 UTC 2027

Time difference in seconds : 0

Expected Time difference in seconds : 30
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputGetLogCleanupTime = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "grep 'Start: Cleanup of AXIS logs' /var/log/boot.log | tail -1 | cut -f1 -d' '"
InputGetLogCleanerStartTime = tac /export/home/<USER>/senovision/logfiles/LastlogCleaner.log | grep -i -m 1 "Starting logCleaner" | /bin/egrep -o "[0-9]{4}-[0-9]{2}-[0-9]{2},[0-9]{2}:[0-9]{2}:[0-9]{2}"
ExpectedMaxTimeDiff = 30
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected time difference found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A038 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A038 ***************


Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "grep 'Start: Cleanup of AXIS logs' /var/log/boot.log | tail -1 | cut -f1 -d' '"
Time taken to execute script 480 ms

Axis logs Cleanup Start time :2027-07-01,12:41:17

Executing : bash -c tac /export/home/<USER>/senovision/logfiles/LastlogCleaner.log | grep -i -m 1 "Starting logCleaner" | /bin/egrep -o "[0-9]{4}-[0-9]{2}-[0-9]{2},[0-9]{2}:[0-9]{2}:[0-9]{2}"
Time taken to execute script 4 ms

Log Cleaner Started time :2027-07-01,12:41:17

Calculating time difference

Start date : Thu Jul 01 12:41:17 UTC 2027
End date : Thu Jul 01 12:41:17 UTC 2027

Time difference in seconds : 0

Expected Time difference in seconds : 30
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputGetLogCleanupTime = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "grep 'Start: Cleanup of AXIS logs' /var/log/boot.log | tail -1 | cut -f1 -d' '"
InputGetLogCleanerStartTime = tac /export/home/<USER>/senovision/logfiles/LastlogCleaner.log | grep -i -m 1 "Starting logCleaner" | /bin/egrep -o "[0-9]{4}-[0-9]{2}-[0-9]{2},[0-9]{2}:[0-9]{2}:[0-9]{2}"
ExpectedMaxTimeDiff = 30
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected time difference found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A038 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A038 ***************


Executing : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "grep 'Start: Cleanup of AXIS logs' /var/log/boot.log | tail -1 | cut -f1 -d' '"
Time taken to execute script 318 ms

Axis logs Cleanup Start time :2025-07-01,14:08:50

Executing : bash -c tac /export/home/<USER>/senovision/logfiles/LastlogCleaner.log | grep -i -m 1 "Starting logCleaner" | /bin/egrep -o "[0-9]{4}-[0-9]{2}-[0-9]{2},[0-9]{2}:[0-9]{2}:[0-9]{2}"
Time taken to execute script 16 ms

Log Cleaner Started time :2025-07-01,14:08:50

Calculating time difference

Start date : Tue Jul 01 14:08:50 GMT 2025
End date : Tue Jul 01 14:08:50 GMT 2025

Time difference in seconds : 0

Expected Time difference in seconds : 30
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputGetLogCleanupTime = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "grep 'Start: Cleanup of AXIS logs' /var/log/boot.log | tail -1 | cut -f1 -d' '"
InputGetLogCleanerStartTime = tac /export/home/<USER>/senovision/logfiles/LastlogCleaner.log | grep -i -m 1 "Starting logCleaner" | /bin/egrep -o "[0-9]{4}-[0-9]{2}-[0-9]{2},[0-9]{2}:[0-9]{2}:[0-9]{2}"
ExpectedMaxTimeDiff = 30
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
Expected time difference found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A038 ***************

Test Result : SUCCESS
