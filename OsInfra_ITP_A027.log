USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A027 ***************

Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'No'
Current Time : 01-07-2027 07:34:49.318 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 30 ms

Squish Server is already running with pid 3019

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT_CLICK_CONFIRMATION_NO
Time taken to execute script 50431 ms
Current Time : 01-07-2027 07:35:39.786 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'Yes'
Current Time : 01-07-2027 07:35:39.786 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3019

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT
Time taken to execute script 8815 ms
Current Time : 01-07-2027 07:35:48.609 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click on Log out and to Click 'Yes' on Confirmation pop up
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOGOUT_CLICK_CONFIRMATION_NO,tst_User_Logout_Login:LOGOUT,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Failed to Click on Log out and to Click 'Yes' on Confirmation pop up 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A027 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A027 ***************

Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'No'
Current Time : 01-07-2027 10:05:33.110 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT_CLICK_CONFIRMATION_NO
Time taken to execute script 50414 ms
Current Time : 01-07-2027 10:06:23.559 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'Yes'
Current Time : 01-07-2027 10:06:23.564 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 10 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT
Time taken to execute script 15772 ms
Current Time : 01-07-2027 10:06:39.346 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Log in as clinical user ,verify PDM displays
Current Time : 01-07-2027 10:06:39.347 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 12 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 68957 ms
Current Time : 01-07-2027 10:07:48.316 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged in as clinical user ,verified PDM displays successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOGOUT_CLICK_CONFIRMATION_NO,tst_User_Logout_Login:LOGOUT,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Logged in as clinical user ,verified PDM displays successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A027 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A027 ***************

Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'No'
Current Time : 01-07-2027 10:45:37.618 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 2980

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT_CLICK_CONFIRMATION_NO
Time taken to execute script 8863 ms
Current Time : 01-07-2027 10:45:46.518 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click on Log out and to Click 'No' on Confirmation pop up
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOGOUT_CLICK_CONFIRMATION_NO,tst_User_Logout_Login:LOGOUT,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Failed to Click on Log out and to Click 'No' on Confirmation pop up 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A027 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A027 ***************

Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'No'
Current Time : 01-07-2027 11:16:05.436 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 34 ms

Squish Server is already running with pid 3053

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT_CLICK_CONFIRMATION_NO
Time taken to execute script 4684 ms
Current Time : 01-07-2027 11:16:10.163 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click on Log out and to Click 'No' on Confirmation pop up
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOGOUT_CLICK_CONFIRMATION_NO,tst_User_Logout_Login:LOGOUT,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Failed to Click on Log out and to Click 'No' on Confirmation pop up 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A027 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A027 ***************

Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'No'
Current Time : 01-07-2027 11:50:24.097 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT_CLICK_CONFIRMATION_NO
Time taken to execute script 50428 ms
Current Time : 01-07-2027 11:51:14.561 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'Yes'
Current Time : 01-07-2027 11:51:14.564 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT
Time taken to execute script 15782 ms
Current Time : 01-07-2027 11:51:30.354 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Log in as clinical user ,verify PDM displays
Current Time : 01-07-2027 11:51:30.354 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 68952 ms
Current Time : 01-07-2027 11:52:39.314 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged in as clinical user ,verified PDM displays successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOGOUT_CLICK_CONFIRMATION_NO,tst_User_Logout_Login:LOGOUT,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Logged in as clinical user ,verified PDM displays successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A027 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A027 ***************

Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'No'
Current Time : 01-07-2027 12:22:39.013 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT_CLICK_CONFIRMATION_NO
Time taken to execute script 50324 ms
Current Time : 01-07-2027 12:23:29.372 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'Yes'
Current Time : 01-07-2027 12:23:29.373 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT
Time taken to execute script 15712 ms
Current Time : 01-07-2027 12:23:45.093 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Log in as clinical user ,verify PDM displays
Current Time : 01-07-2027 12:23:45.093 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 68799 ms
Current Time : 01-07-2027 12:24:53.900 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged in as clinical user ,verified PDM displays successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOGOUT_CLICK_CONFIRMATION_NO,tst_User_Logout_Login:LOGOUT,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Logged in as clinical user ,verified PDM displays successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A027 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A027 ***************

Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'No'
Current Time : 01-07-2027 13:08:23.586 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT_CLICK_CONFIRMATION_NO
Time taken to execute script 50334 ms
Current Time : 01-07-2027 13:09:13.955 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'Yes'
Current Time : 01-07-2027 13:09:13.960 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT
Time taken to execute script 15758 ms
Current Time : 01-07-2027 13:09:29.726 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Log in as clinical user ,verify PDM displays
Current Time : 01-07-2027 13:09:29.726 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 11965 ms
Current Time : 01-07-2027 13:09:41.699 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Log in as clinical user and verify PDM displays
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOGOUT_CLICK_CONFIRMATION_NO,tst_User_Logout_Login:LOGOUT,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Failed to Log in as clinical user and verify PDM displays
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A027 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A027 ***************

Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'No'
Current Time : 01-07-2025 13:50:10.711 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT_CLICK_CONFIRMATION_NO
Time taken to execute script 51027 ms
Current Time : 01-07-2025 13:51:01.774 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Click On power dropdown menu- ->Log out ,Confirmation pop up appears,Click on 'Yes'
Current Time : 01-07-2025 13:51:01.779 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGOUT
Time taken to execute script 15678 ms
Current Time : 01-07-2025 13:51:17.465 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Log in as clinical user ,verify PDM displays
Current Time : 01-07-2025 13:51:17.466 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_User_Logout_Login --tags @LOGIN_AS_CLINICAL_USER
Time taken to execute script 68969 ms
Current Time : 01-07-2025 13:52:26.442 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A027_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Logged in as clinical user ,verified PDM displays successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_User_Logout_Login:LOGOUT_CLICK_CONFIRMATION_NO,tst_User_Logout_Login:LOGOUT,tst_User_Logout_Login:LOGIN_AS_CLINICAL_USER
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

 Clicked on Log out, Confirmation pop up appeared ,Clicked on 'No' successfuly 
Clicked on Log out, Confirmation pop up appeared ,Clicked on 'Yes' successfuly 
Logged in as clinical user ,verified PDM displays successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A027 ***************

Test Result : SUCCESS
