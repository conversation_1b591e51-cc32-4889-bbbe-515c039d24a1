USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2027 07:34:12.340 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3019

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
Time taken to execute script 36905 ms
Current Time : 01-07-2027 07:34:49.261 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A026_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputClearDB = /export/home/<USER>/senovision/scripts/clearDB.sh
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

ClearDB executed
Sigmoid Test Pattern is not deleted from browser
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A026 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2027 10:04:55.472 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
Time taken to execute script 37560 ms
Current Time : 01-07-2027 10:05:33.048 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A026_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputClearDB = /export/home/<USER>/senovision/scripts/clearDB.sh
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

ClearDB executed
Sigmoid Test Pattern is not deleted from browser
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A026 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2027 10:45:20.119 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 2980

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
Time taken to execute script 17423 ms
Current Time : 01-07-2027 10:45:37.557 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A026_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputClearDB = /export/home/<USER>/senovision/scripts/clearDB.sh
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

ClearDB executed
Sigmoid Test Pattern is not found in browser
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A026 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2027 11:05:53.767 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3047

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
Time taken to execute script 4662 ms
Current Time : 01-07-2027 11:05:58.443 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A026_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputClearDB = /export/home/<USER>/senovision/scripts/clearDB.sh
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

ClearDB executed
Sigmoid Test Pattern is not found in browser
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A026 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2027 11:15:51.177 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 11 ms

Squish Server is already running with pid 3053

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2027 11:49:47.284 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 9 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
Time taken to execute script 36730 ms
Current Time : 01-07-2027 11:50:24.031 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A026_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputClearDB = /export/home/<USER>/senovision/scripts/clearDB.sh
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

ClearDB executed
Sigmoid Test Pattern is not deleted from browser
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A026 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2027 12:22:02.099 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 9 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
Time taken to execute script 36823 ms
Current Time : 01-07-2027 12:22:38.939 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A026_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputClearDB = /export/home/<USER>/senovision/scripts/clearDB.sh
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

ClearDB executed
Sigmoid Test Pattern is not deleted from browser
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A026 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2027 12:57:50.703 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3036

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
Time taken to execute script 4865 ms
Current Time : 01-07-2027 12:57:55.583 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A026_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputClearDB = /export/home/<USER>/senovision/scripts/clearDB.sh
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

ClearDB executed
Sigmoid Test Pattern is not found in browser
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A026 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2027 13:07:46.805 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 8 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
Time taken to execute script 36697 ms
Current Time : 01-07-2027 13:08:23.517 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A026_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputClearDB = /export/home/<USER>/senovision/scripts/clearDB.sh
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

ClearDB executed
Sigmoid Test Pattern is not deleted from browser
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A026 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A026 ***************



Clearing DB
Executing command : bash -c /export/home/<USER>/senovision/scripts/clearDB.sh


Checking for Sigmoid Test Pattern
Current Time : 01-07-2025 13:49:33.990 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 7 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_Browser --tags @VERIFY_SIGMOID_TEST_PATTERN_PRESENT
Time taken to execute script 36644 ms
Current Time : 01-07-2025 13:50:10.649 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A026_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputClearDB = /export/home/<USER>/senovision/scripts/clearDB.sh
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

ClearDB executed
Sigmoid Test Pattern is not deleted from browser
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A026 ***************

Test Result : SUCCESS
