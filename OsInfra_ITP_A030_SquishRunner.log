
**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - VERIFY_EA3_LOGON_SCREEN_IS_VISIBLE
TestCase Start Time - 01-07-2027 10:12:05.093 AM
**********************************************************************************

2027-07-01T10:12:05	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30060001
AUTID: 30060003
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30060003
RUNNERID: 30060004
AUTID: 30060005
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30060005
2027-07-01T10:12:05	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:165: Start Scenario	Verify EA3 logon screen is visible or not
2027-07-01T10:12:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:166: Start Step	Given Logon is up and running
2027-07-01T10:12:16	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:12:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T10:12:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T10:12:16	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:12:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:166: End Step	Given Logon is up and running
2027-07-01T10:12:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:165: End Scenario	Verify EA3 logon screen is visible or not
2027-07-01T10:12:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:12:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:12:16	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T10:12:17	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   2
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   2
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:12:17.060 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 10:12:17.061 AM
**********************************************************************************

2027-07-01T10:12:17	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30060006
AUTID: 30060008
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30060008
RUNNERID: 30060009
AUTID: 30060010
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30060010
RUNNERID: 30060011
AUTID: 30060012
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30060012
2027-07-01T10:12:17	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T10:12:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T10:12:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T10:12:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2027-07-01T10:12:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2027-07-01T10:12:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2027-07-01T10:12:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2027-07-01T10:12:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2027-07-01T10:12:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2027-07-01T10:12:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2027-07-01T10:12:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2027-07-01T10:12:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2027-07-01T10:12:44	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 30060013
2027-07-01T10:12:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T10:12:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2027-07-01T10:12:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2027-07-01T10:13:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:13:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_10:13:25.PNG	
2027-07-01T10:13:25	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Squish_Wrapper/Wrapper.py:713: Script Error	RuntimeError: Screenshot taken from the desktop is invalid (Perhaps the AUT has quit/crashed unexpectedly?)
2027-07-01T10:13:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2027-07-01T10:13:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T10:13:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:13:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:13:25	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T10:13:25	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************
Runner::call: socket of context -1 is null

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:13:25.599 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - VERIFY_EA3_LOGON_SCREEN_IS_VISIBLE
TestCase Start Time - 01-07-2027 11:56:58.048 AM
**********************************************************************************

2027-07-01T11:56:58	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30110001
AUTID: 30110003
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30110003
RUNNERID: 30110004
AUTID: 30110005
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30110005
2027-07-01T11:56:58	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:165: Start Scenario	Verify EA3 logon screen is visible or not
2027-07-01T11:57:03	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:166: Start Step	Given Logon is up and running
2027-07-01T11:57:09	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:57:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T11:57:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T11:57:09	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:57:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:166: End Step	Given Logon is up and running
2027-07-01T11:57:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:165: End Scenario	Verify EA3 logon screen is visible or not
2027-07-01T11:57:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:57:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:57:09	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T11:57:10	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   2
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   2
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:57:10.133 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 11:57:10.138 AM
**********************************************************************************

2027-07-01T11:57:10	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30110006
AUTID: 30110008
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30110008
RUNNERID: 30110009
AUTID: 30110010
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30110010
RUNNERID: 30110011
AUTID: 30110012
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30110012
2027-07-01T11:57:10	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T11:57:15	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T11:57:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:57:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T11:57:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T11:57:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:57:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T11:57:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2027-07-01T11:57:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T11:57:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T11:57:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2027-07-01T11:57:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2027-07-01T11:57:22	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2027-07-01T11:57:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2027-07-01T11:57:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2027-07-01T11:57:22	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2027-07-01T11:57:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2027-07-01T11:57:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2027-07-01T11:57:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2027-07-01T11:57:22	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2027-07-01T11:57:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2027-07-01T11:57:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2027-07-01T11:57:37	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2027-07-01T11:57:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 30110013
AUTID: 30110014
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30110014
2027-07-01T11:57:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T11:57:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2027-07-01T11:57:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2027-07-01T11:58:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:58:18	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:58:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2027-07-01T11:58:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T11:58:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:58:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:58:18	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T11:58:19	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:58:19.303 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - VERIFY_EA3_LOGON_SCREEN_IS_VISIBLE
TestCase Start Time - 01-07-2027 12:29:12.200 PM
**********************************************************************************

2027-07-01T12:29:12	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 29730001
AUTID: 29730003
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730003
RUNNERID: 29730004
AUTID: 29730005
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730005
2027-07-01T12:29:12	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:165: Start Scenario	Verify EA3 logon screen is visible or not
2027-07-01T12:29:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:166: Start Step	Given Logon is up and running
2027-07-01T12:29:23	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:29:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T12:29:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T12:29:23	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:29:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:166: End Step	Given Logon is up and running
2027-07-01T12:29:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:165: End Scenario	Verify EA3 logon screen is visible or not
2027-07-01T12:29:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:29:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:29:23	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T12:29:24	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   2
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   2
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:29:24.187 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 12:29:24.192 PM
**********************************************************************************

2027-07-01T12:29:24	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 29730006
AUTID: 29730008
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730008
RUNNERID: 29730009
AUTID: 29730010
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730010
RUNNERID: 29730011
AUTID: 29730012
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730012
2027-07-01T12:29:24	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T12:29:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T12:29:35	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T12:29:35	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2027-07-01T12:29:35	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2027-07-01T12:29:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2027-07-01T12:29:36	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2027-07-01T12:29:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2027-07-01T12:29:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2027-07-01T12:29:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2027-07-01T12:29:36	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2027-07-01T12:29:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2027-07-01T12:29:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2027-07-01T12:29:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2027-07-01T12:29:52	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 29730013
AUTID: 29730014
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 29730014
2027-07-01T12:29:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T12:29:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2027-07-01T12:29:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2027-07-01T12:30:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:30:32	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:30:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2027-07-01T12:30:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T12:30:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:30:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:30:32	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T12:30:33	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:30:33.242 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - VERIFY_EA3_LOGON_SCREEN_IS_VISIBLE
TestCase Start Time - 01-07-2025 13:56:44.076 PM
**********************************************************************************

2025-07-01T13:56:44	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30820001
AUTID: 30820003
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820003
RUNNERID: 30820004
AUTID: 30820005
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820005
2025-07-01T13:56:44	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:165: Start Scenario	Verify EA3 logon screen is visible or not
2025-07-01T13:56:49	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:166: Start Step	Given Logon is up and running
2025-07-01T13:56:55	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:56:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2025-07-01T13:56:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2025-07-01T13:56:55	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:56:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:166: End Step	Given Logon is up and running
2025-07-01T13:56:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:165: End Scenario	Verify EA3 logon screen is visible or not
2025-07-01T13:56:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:56:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:56:55	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2025-07-01T13:56:56	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   2
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   2
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:56:56.115 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2025 13:56:56.116 PM
**********************************************************************************

2025-07-01T13:56:56	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30820006
AUTID: 30820008
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820008
RUNNERID: 30820009
AUTID: 30820010
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820010
RUNNERID: 30820011
AUTID: 30820012
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820012
2025-07-01T13:56:56	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2025-07-01T13:57:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2025-07-01T13:57:07	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2025-07-01T13:57:07	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2025-07-01T13:57:07	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2025-07-01T13:57:07	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2025-07-01T13:57:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2025-07-01T13:57:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2025-07-01T13:57:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2025-07-01T13:57:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2025-07-01T13:57:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2025-07-01T13:57:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2025-07-01T13:57:24	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 30820013
AUTID: 30820014
AUTHOST: 127.0.0.1
AUTPORT: 4322
RUNNERID: -1
application registered server: 127.0.0.1 4322 aut: java 30820014
2025-07-01T13:57:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2025-07-01T13:57:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2025-07-01T13:57:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2025-07-01T13:58:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:58:04	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:58:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2025-07-01T13:58:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2025-07-01T13:58:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:58:04	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:58:04	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2025-07-01T13:58:05	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:58:05.164 PM
**********************************************************************************


