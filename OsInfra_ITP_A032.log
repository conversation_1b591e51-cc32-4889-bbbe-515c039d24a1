USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A032 ***************


Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c touch /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp
Time taken to execute script 254 ms
created the temp files /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp -pf  /etc/cron.daily/tmpwatch  /etc/cron.daily/tmpwatch.org
Time taken to execute script 185 ms
Copied the orginal /etc/cron.daily/tmpwatch file to /etc/cron.daily/tmpwatch.org successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/1d/0d/g' /etc/cron.daily/tmpwatch
Time taken to execute script 180 ms
Changed 1d to 0d In /etc/cron.daily/tmpwatch successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /etc/cron.daily/tmpwatch
Time taken to execute script 243 ms
Executed /etc/cron.daily/tmpwatch successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c find /tmp/ -mmin +0.01  -name "OsInfra_ITP_A032*.tmp"|wc -l
Time taken to execute script 182 ms
 Failed to verify OsInfra_ITP_A032*.tmp files in /tmp 
Executing command : bash -c (/usr/bin/sleep 10;/usr/bin/sudo /sbin/reboot) &
Reboot executed successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToTouchTmpwatchFile = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,touch /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp
InputToCopyTmpwatchFile = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,cp -pf  /etc/cron.daily/tmpwatch  /etc/cron.daily/tmpwatch.org
InputToExecuteTmpwatch = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/etc/cron.daily/tmpwatch
InputToReplace1dTo0d = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,sed -i 's/1d/0d/g' /etc/cron.daily/tmpwatch
InputToFindJFFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,find /tmp/ -mmin +0.01  -name "OsInfra_ITP_A032*.tmp"|wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Changed 1d to 0d In /etc/cron.daily/tmpwatch successfully
Executed /etc/cron.daily/tmpwatch successfully 
Failed to verify OsInfra_ITP_A032*.tmp files in /tmp 
System is rebooted
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A032 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A032 ***************


Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c touch /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp
Time taken to execute script 2872 ms
created the temp files /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp -pf  /etc/cron.daily/tmpwatch  /etc/cron.daily/tmpwatch.org
Time taken to execute script 16 ms
Copied the orginal /etc/cron.daily/tmpwatch file to /etc/cron.daily/tmpwatch.org successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/1d/0d/g' /etc/cron.daily/tmpwatch
Time taken to execute script 22 ms
Changed 1d to 0d In /etc/cron.daily/tmpwatch successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /etc/cron.daily/tmpwatch
Time taken to execute script 14 ms
Executed /etc/cron.daily/tmpwatch successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c find /tmp/ -mmin +0.01  -name "OsInfra_ITP_A032*.tmp"|wc -l
Time taken to execute script 9 ms
Caught exception while executing testcase :OsInfra_ITP_A032 of type Index: 3, Size: 3
Executing command : bash -c (/usr/bin/sleep 10;/usr/bin/sudo /sbin/reboot) &
Reboot executed successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToTouchTmpwatchFile = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,touch /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp
InputToCopyTmpwatchFile = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,cp -pf  /etc/cron.daily/tmpwatch  /etc/cron.daily/tmpwatch.org
InputToExecuteTmpwatch = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/etc/cron.daily/tmpwatch
InputToReplace1dTo0d = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,sed -i 's/1d/0d/g' /etc/cron.daily/tmpwatch
InputToFindJFFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,find /tmp/ -mmin +0.01  -name "OsInfra_ITP_A032*.tmp"|wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Changed 1d to 0d In /etc/cron.daily/tmpwatch successfully
Executed /etc/cron.daily/tmpwatch successfully 
System is rebooted
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A032 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A032 ***************


Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c touch /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp
Time taken to execute script 298 ms
created the temp files /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp -pf  /etc/cron.daily/tmpwatch  /etc/cron.daily/tmpwatch.org
Time taken to execute script 238 ms
Copied the orginal /etc/cron.daily/tmpwatch file to /etc/cron.daily/tmpwatch.org successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/1d/0d/g' /etc/cron.daily/tmpwatch
Time taken to execute script 296 ms
Changed 1d to 0d In /etc/cron.daily/tmpwatch successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /etc/cron.daily/tmpwatch
Time taken to execute script 434 ms
Executed /etc/cron.daily/tmpwatch successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c find /tmp/ -mmin +0.01  -name "OsInfra_ITP_A032*.tmp"|wc -l
Time taken to execute script 311 ms
Verified No OsInfra_ITP_A032*.tmp files is seen in /tmp
Executing command : bash -c (/usr/bin/sleep 10;/usr/bin/sudo /sbin/reboot) &
Reboot executed successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToTouchTmpwatchFile = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,touch /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp
InputToCopyTmpwatchFile = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,cp -pf  /etc/cron.daily/tmpwatch  /etc/cron.daily/tmpwatch.org
InputToExecuteTmpwatch = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/etc/cron.daily/tmpwatch
InputToReplace1dTo0d = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,sed -i 's/1d/0d/g' /etc/cron.daily/tmpwatch
InputToFindJFFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,find /tmp/ -mmin +0.01  -name "OsInfra_ITP_A032*.tmp"|wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Changed 1d to 0d In /etc/cron.daily/tmpwatch successfully
Executed /etc/cron.daily/tmpwatch successfully 
Successfully Verified that No OsInfra_ITP_A032*.tmp files is seen in /tmp
System is rebooted
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A032 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A032 ***************


Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c touch /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp
Time taken to execute script 453 ms
created the temp files /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c cp -pf  /etc/cron.daily/tmpwatch  /etc/cron.daily/tmpwatch.org
Time taken to execute script 192 ms
Copied the orginal /etc/cron.daily/tmpwatch file to /etc/cron.daily/tmpwatch.org successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c sed -i 's/1d/0d/g' /etc/cron.daily/tmpwatch
Time taken to execute script 272 ms
Changed 1d to 0d In /etc/cron.daily/tmpwatch successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c /etc/cron.daily/tmpwatch
Time taken to execute script 332 ms
Executed /etc/cron.daily/tmpwatch successfully

Executing : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c find /tmp/ -mmin +0.01  -name "OsInfra_ITP_A032*.tmp"|wc -l
Time taken to execute script 262 ms
Verified No OsInfra_ITP_A032*.tmp files is seen in /tmp
Executing command : bash -c (/usr/bin/sleep 10;/usr/bin/sudo /sbin/reboot) &
Reboot executed successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputToTouchTmpwatchFile = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,touch /tmp/OsInfra_ITP_A032_1.tmp /tmp/OsInfra_ITP_A032_2.tmp /tmp/OsInfra_ITP_A032_3.tmp
InputToCopyTmpwatchFile = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,cp -pf  /etc/cron.daily/tmpwatch  /etc/cron.daily/tmpwatch.org
InputToExecuteTmpwatch = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,/etc/cron.daily/tmpwatch
InputToReplace1dTo0d = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,sed -i 's/1d/0d/g' /etc/cron.daily/tmpwatch
InputToFindJFFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh,-u,root,-p,Install@123,-c,find /tmp/ -mmin +0.01  -name "OsInfra_ITP_A032*.tmp"|wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Changed 1d to 0d In /etc/cron.daily/tmpwatch successfully
Executed /etc/cron.daily/tmpwatch successfully 
Successfully Verified that No OsInfra_ITP_A032*.tmp files is seen in /tmp
System is rebooted
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A032 ***************

Test Result : SUCCESS
