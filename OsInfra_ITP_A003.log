USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 30 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 30 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 31 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 31 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 30 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 31 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 30 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A003 ***************


Checking if all RPMs are installed successfully

Executing : bash -c grep -i "is not installed" /export/home/<USER>/*.log
Time taken to execute script 29 ms

Error logs which can be ignored : /export/home/<USER>/install_cse.log

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...

Ignoring log : /export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed

Ignoring log : /export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'

Ignoring log : /export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.

Ignoring log : /export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputCheckInstallationStatus = grep -i "is not installed" /export/home/<USER>/*.log
OutputIgnoreErrorLogs = /export/home/<USER>/install_cse.log
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Installation errors found : /export/home/<USER>/install_cse.log:2027-06-30 14:17:03,355 iCore is not installed or installed incorrectly. No changes will be done...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:03,394 iCore is not installed or installed incorrectly. Continuing with default configuration...
/export/home/<USER>/install_cse.log:2027-06-30 14:17:05,003 iCore is not installed. No changes are needed
/export/home/<USER>/install_cse.log:+ echo 'Dicom Server is not installed. Nothing to uninstall.'
/export/home/<USER>/install_cse.log:Dicom Server is not installed. Nothing to uninstall.
/export/home/<USER>/install_cse.log:EPD Web App is not installed. Nothing to uninstall.

No unexpected errors found in RPMs installation
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A003 ***************

Test Result : SUCCESS
