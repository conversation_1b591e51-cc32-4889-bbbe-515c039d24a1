USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A020 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************

Running command : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
Std Input Stream : Executing::>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : 
Std Input Stream : Actual output : aaaaa
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRunLogger = :>/var/log/syslog;logger aaaaa;cat /var/log/syslog | rev | cut -f1 -d ":"|rev
Std Input Stream : ExpectedOutput = aaaaa
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A011 ***************
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputVerifyLogger = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A011
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A020 ***************

Test Result : SUCCESS
