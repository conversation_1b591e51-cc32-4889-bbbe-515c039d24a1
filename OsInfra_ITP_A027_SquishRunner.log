
**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT_CLICK_CONFIRMATION_NO
TestCase Start Time - 01-07-2027 07:34:49.318 AM
**********************************************************************************

2027-07-01T07:34:49	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30190052
AUTID: 30190054
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30190054
RUNNERID: 30190055
AUTID: 30190056
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30190056
2027-07-01T07:34:49	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: Start Scenario	Verify user logout
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: Start Step	Given PDM is UP
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T07:34:55	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: End Step	Given PDM is UP
2027-07-01T07:34:55	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: Start Step	When I clicked on Power Icon
2027-07-01T07:34:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T07:34:56	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T07:34:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: End Step	When I clicked on Power Icon
2027-07-01T07:34:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: Start Step	And I select Log out from System power drop down menu
2027-07-01T07:34:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2027-07-01T07:34:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2027-07-01T07:34:58	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2027-07-01T07:34:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: End Step	And I select Log out from System power drop down menu
2027-07-01T07:34:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: Start Step	And Clicked on Logout confirmation No button
2027-07-01T07:34:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation No button	
2027-07-01T07:34:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation NO button is performed	
2027-07-01T07:34:58	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation NO button	
2027-07-01T07:34:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: End Step	And Clicked on Logout confirmation No button
2027-07-01T07:34:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: Start Step	Then Verify PDM is Up and Running
2027-07-01T07:35:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T07:35:39	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T07:35:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: End Step	Then Verify PDM is Up and Running
2027-07-01T07:35:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: End Scenario	Verify user logout
2027-07-01T07:35:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T07:35:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T07:35:39	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T07:35:39	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 07:35:39.786 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT
TestCase Start Time - 01-07-2027 07:35:39.786 AM
**********************************************************************************

2027-07-01T07:35:39	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30190057
AUTID: 30190059
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30190059
2027-07-01T07:35:39	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: Start Scenario	Verify user logout
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: Start Step	Given PDM is UP
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T07:35:45	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: End Step	Given PDM is UP
2027-07-01T07:35:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: Start Step	When I clicked on Power Icon
2027-07-01T07:35:47	WARNING   	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:35: ERROR :button click object Power Icon could not be found	
2027-07-01T07:35:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_07:35:47.PNG	
2027-07-01T07:35:48	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:58: Script Error	RuntimeError: TEST FAILURE :Failed to perform button click on Power Icon:
2027-07-01T07:35:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: End Step	When I clicked on Power Icon
2027-07-01T07:35:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: Skip Step	And I select Log out from System power drop down menu
2027-07-01T07:35:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: Skip Step	And Clicked on Logout confirmation Yes button
2027-07-01T07:35:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: Skip Step	Then Logon is up and running
2027-07-01T07:35:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: End Scenario	Verify user logout
2027-07-01T07:35:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T07:35:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T07:35:48	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T07:35:48	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   1
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   1
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   1
*******************************************************
ERROR:root:button click object Power Icon could not be found

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 07:35:48.609 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT_CLICK_CONFIRMATION_NO
TestCase Start Time - 01-07-2027 10:05:33.110 AM
**********************************************************************************

2027-07-01T10:05:33	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 28010052
AUTID: 28010054
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 28010054
RUNNERID: 28010055
AUTID: 28010056
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 28010056
2027-07-01T10:05:33	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: Start Scenario	Verify user logout
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: Start Step	Given PDM is UP
2027-07-01T10:05:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:05:39	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:05:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: End Step	Given PDM is UP
2027-07-01T10:05:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: Start Step	When I clicked on Power Icon
2027-07-01T10:05:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T10:05:39	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T10:05:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: End Step	When I clicked on Power Icon
2027-07-01T10:05:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: Start Step	And I select Log out from System power drop down menu
2027-07-01T10:05:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2027-07-01T10:05:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2027-07-01T10:05:41	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2027-07-01T10:05:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: End Step	And I select Log out from System power drop down menu
2027-07-01T10:05:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: Start Step	And Clicked on Logout confirmation No button
2027-07-01T10:05:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation No button	
2027-07-01T10:05:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation NO button is performed	
2027-07-01T10:05:42	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation NO button	
2027-07-01T10:05:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: End Step	And Clicked on Logout confirmation No button
2027-07-01T10:05:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: Start Step	Then Verify PDM is Up and Running
2027-07-01T10:06:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:06:23	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:06:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: End Step	Then Verify PDM is Up and Running
2027-07-01T10:06:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: End Scenario	Verify user logout
2027-07-01T10:06:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:06:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:06:23	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T10:06:23	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:06:23.559 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT
TestCase Start Time - 01-07-2027 10:06:23.564 AM
**********************************************************************************

2027-07-01T10:06:23	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 28010057
AUTID: 28010059
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 28010059
RUNNERID: 28010060
AUTID: 28010061
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 28010061
RUNNERID: 28010062
AUTID: 28010063
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 28010063
2027-07-01T10:06:23	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: Start Scenario	Verify user logout
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: Start Step	Given PDM is UP
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:06:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: End Step	Given PDM is UP
2027-07-01T10:06:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: Start Step	When I clicked on Power Icon
2027-07-01T10:06:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T10:06:30	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T10:06:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: End Step	When I clicked on Power Icon
2027-07-01T10:06:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: Start Step	And I select Log out from System power drop down menu
2027-07-01T10:06:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2027-07-01T10:06:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2027-07-01T10:06:32	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2027-07-01T10:06:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: End Step	And I select Log out from System power drop down menu
2027-07-01T10:06:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: Start Step	And Clicked on Logout confirmation Yes button
2027-07-01T10:06:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation Yes button	
2027-07-01T10:06:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation YES button is performed	
2027-07-01T10:06:33	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation YES button	
2027-07-01T10:06:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: End Step	And Clicked on Logout confirmation Yes button
2027-07-01T10:06:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: Start Step	Then Logon is up and running
2027-07-01T10:06:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:06:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T10:06:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T10:06:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:06:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: End Step	Then Logon is up and running
2027-07-01T10:06:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: End Scenario	Verify user logout
2027-07-01T10:06:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:06:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:06:38	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T10:06:39	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:06:39.346 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 10:06:39.347 AM
**********************************************************************************

2027-07-01T10:06:39	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 28010064
AUTID: 28010066
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 28010066
RUNNERID: 28010067
AUTID: 28010068
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 28010068
RUNNERID: 28010069
AUTID: 28010070
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 28010070
2027-07-01T10:06:39	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T10:06:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T10:06:50	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:06:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T10:06:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T10:06:50	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T10:06:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T10:06:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2027-07-01T10:06:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T10:06:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T10:06:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2027-07-01T10:06:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2027-07-01T10:06:51	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2027-07-01T10:06:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2027-07-01T10:06:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2027-07-01T10:06:51	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2027-07-01T10:06:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2027-07-01T10:06:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2027-07-01T10:06:51	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2027-07-01T10:06:51	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2027-07-01T10:07:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2027-07-01T10:07:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2027-07-01T10:07:06	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2027-07-01T10:07:07	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 28010071
AUTID: 28010072
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 28010072
2027-07-01T10:07:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T10:07:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2027-07-01T10:07:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2027-07-01T10:07:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:07:47	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:07:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2027-07-01T10:07:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T10:07:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:07:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:07:47	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T10:07:48	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:07:48.316 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT_CLICK_CONFIRMATION_NO
TestCase Start Time - 01-07-2027 10:45:37.618 AM
**********************************************************************************

2027-07-01T10:45:37	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 29800036
AUTID: 29800038
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 29800038
2027-07-01T10:45:37	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: Start Scenario	Verify user logout
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: Start Step	Given PDM is UP
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T10:45:43	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: End Step	Given PDM is UP
2027-07-01T10:45:43	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: Start Step	When I clicked on Power Icon
2027-07-01T10:45:45	WARNING   	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:35: ERROR :button click object Power Icon could not be found	
2027-07-01T10:45:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_10:45:45.PNG	
2027-07-01T10:45:46	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:58: Script Error	RuntimeError: TEST FAILURE :Failed to perform button click on Power Icon:
2027-07-01T10:45:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: End Step	When I clicked on Power Icon
2027-07-01T10:45:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: Skip Step	And I select Log out from System power drop down menu
2027-07-01T10:45:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: Skip Step	And Clicked on Logout confirmation No button
2027-07-01T10:45:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: Skip Step	Then Verify PDM is Up and Running
2027-07-01T10:45:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: End Scenario	Verify user logout
2027-07-01T10:45:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T10:45:46	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T10:45:46	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T10:45:46	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   1
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   1
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   1
*******************************************************
ERROR:root:button click object Power Icon could not be found

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 10:45:46.518 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT_CLICK_CONFIRMATION_NO
TestCase Start Time - 01-07-2027 11:16:05.436 AM
**********************************************************************************

2027-07-01T11:16:05	START     	Start 'TestCases'             	Test 'TestCases' started
2027-07-01T11:16:05	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: Start Scenario	Verify user logout
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: Start Step	Given PDM is UP
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_11:16:09.PNG	
2027-07-01T11:16:09	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Squish_Wrapper/Wrapper.py:713: Script Error	RuntimeError: Screenshot taken from the desktop is invalid (Perhaps the AUT has quit/crashed unexpectedly?)
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: End Step	Given PDM is UP
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: Skip Step	When I clicked on Power Icon
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: Skip Step	And I select Log out from System power drop down menu
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: Skip Step	And Clicked on Logout confirmation No button
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: Skip Step	Then Verify PDM is Up and Running
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: End Scenario	Verify user logout
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:16:09	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:16:09	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T11:16:10	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   0
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   0
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:16:10.163 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT_CLICK_CONFIRMATION_NO
TestCase Start Time - 01-07-2027 11:50:24.097 AM
**********************************************************************************

2027-07-01T11:50:24	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30460080
AUTID: 30460082
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30460082
RUNNERID: 30460083
AUTID: 30460084
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30460084
2027-07-01T11:50:24	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: Start Scenario	Verify user logout
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: Start Step	Given PDM is UP
2027-07-01T11:50:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:50:30	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:50:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: End Step	Given PDM is UP
2027-07-01T11:50:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: Start Step	When I clicked on Power Icon
2027-07-01T11:50:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T11:50:30	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T11:50:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: End Step	When I clicked on Power Icon
2027-07-01T11:50:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: Start Step	And I select Log out from System power drop down menu
2027-07-01T11:50:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2027-07-01T11:50:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2027-07-01T11:50:32	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2027-07-01T11:50:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: End Step	And I select Log out from System power drop down menu
2027-07-01T11:50:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: Start Step	And Clicked on Logout confirmation No button
2027-07-01T11:50:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation No button	
2027-07-01T11:50:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation NO button is performed	
2027-07-01T11:50:33	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation NO button	
2027-07-01T11:50:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: End Step	And Clicked on Logout confirmation No button
2027-07-01T11:50:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: Start Step	Then Verify PDM is Up and Running
2027-07-01T11:51:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:51:14	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:51:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: End Step	Then Verify PDM is Up and Running
2027-07-01T11:51:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: End Scenario	Verify user logout
2027-07-01T11:51:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:51:14	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:51:14	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T11:51:14	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:51:14.561 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT
TestCase Start Time - 01-07-2027 11:51:14.564 AM
**********************************************************************************

2027-07-01T11:51:14	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30460085
AUTID: 30460087
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30460087
RUNNERID: 30460088
AUTID: 30460089
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30460089
RUNNERID: 30460090
AUTID: 30460091
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30460091
2027-07-01T11:51:14	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: Start Scenario	Verify user logout
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: Start Step	Given PDM is UP
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:51:20	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: End Step	Given PDM is UP
2027-07-01T11:51:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: Start Step	When I clicked on Power Icon
2027-07-01T11:51:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T11:51:21	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T11:51:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: End Step	When I clicked on Power Icon
2027-07-01T11:51:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: Start Step	And I select Log out from System power drop down menu
2027-07-01T11:51:21	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2027-07-01T11:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2027-07-01T11:51:23	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2027-07-01T11:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: End Step	And I select Log out from System power drop down menu
2027-07-01T11:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: Start Step	And Clicked on Logout confirmation Yes button
2027-07-01T11:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation Yes button	
2027-07-01T11:51:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation YES button is performed	
2027-07-01T11:51:24	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation YES button	
2027-07-01T11:51:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: End Step	And Clicked on Logout confirmation Yes button
2027-07-01T11:51:24	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: Start Step	Then Logon is up and running
2027-07-01T11:51:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T11:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T11:51:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: End Step	Then Logon is up and running
2027-07-01T11:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: End Scenario	Verify user logout
2027-07-01T11:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:51:29	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T11:51:30	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:51:30.354 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 11:51:30.354 AM
**********************************************************************************

2027-07-01T11:51:30	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30460092
AUTID: 30460094
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30460094
RUNNERID: 30460095
AUTID: 30460096
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30460096
RUNNERID: 30460097
AUTID: 30460098
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30460098
2027-07-01T11:51:30	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T11:51:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T11:51:41	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:51:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T11:51:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T11:51:41	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T11:51:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T11:51:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2027-07-01T11:51:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T11:51:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T11:51:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2027-07-01T11:51:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2027-07-01T11:51:42	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2027-07-01T11:51:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2027-07-01T11:51:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2027-07-01T11:51:42	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2027-07-01T11:51:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2027-07-01T11:51:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2027-07-01T11:51:42	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2027-07-01T11:51:42	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2027-07-01T11:51:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2027-07-01T11:51:52	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2027-07-01T11:51:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2027-07-01T11:51:58	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 30460099
AUTID: 30460100
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30460100
2027-07-01T11:51:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T11:51:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2027-07-01T11:51:58	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2027-07-01T11:52:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T11:52:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T11:52:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2027-07-01T11:52:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T11:52:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T11:52:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T11:52:38	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T11:52:39	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 11:52:39.314 AM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT_CLICK_CONFIRMATION_NO
TestCase Start Time - 01-07-2027 12:22:39.013 PM
**********************************************************************************

2027-07-01T12:22:39	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130080
AUTID: 30130082
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130082
RUNNERID: 30130083
AUTID: 30130084
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130084
2027-07-01T12:22:39	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: Start Scenario	Verify user logout
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: Start Step	Given PDM is UP
2027-07-01T12:22:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:22:45	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:22:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: End Step	Given PDM is UP
2027-07-01T12:22:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: Start Step	When I clicked on Power Icon
2027-07-01T12:22:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T12:22:45	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T12:22:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: End Step	When I clicked on Power Icon
2027-07-01T12:22:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: Start Step	And I select Log out from System power drop down menu
2027-07-01T12:22:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2027-07-01T12:22:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2027-07-01T12:22:47	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2027-07-01T12:22:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: End Step	And I select Log out from System power drop down menu
2027-07-01T12:22:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: Start Step	And Clicked on Logout confirmation No button
2027-07-01T12:22:47	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation No button	
2027-07-01T12:22:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation NO button is performed	
2027-07-01T12:22:48	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation NO button	
2027-07-01T12:22:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: End Step	And Clicked on Logout confirmation No button
2027-07-01T12:22:48	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: Start Step	Then Verify PDM is Up and Running
2027-07-01T12:23:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:23:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:23:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: End Step	Then Verify PDM is Up and Running
2027-07-01T12:23:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: End Scenario	Verify user logout
2027-07-01T12:23:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:23:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:23:28	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T12:23:29	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:23:29.372 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT
TestCase Start Time - 01-07-2027 12:23:29.373 PM
**********************************************************************************

2027-07-01T12:23:29	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130085
AUTID: 30130087
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130087
RUNNERID: 30130088
AUTID: 30130089
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130089
RUNNERID: 30130090
AUTID: 30130091
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130091
2027-07-01T12:23:29	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: Start Scenario	Verify user logout
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: Start Step	Given PDM is UP
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:23:35	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: End Step	Given PDM is UP
2027-07-01T12:23:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: Start Step	When I clicked on Power Icon
2027-07-01T12:23:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T12:23:36	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T12:23:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: End Step	When I clicked on Power Icon
2027-07-01T12:23:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: Start Step	And I select Log out from System power drop down menu
2027-07-01T12:23:36	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2027-07-01T12:23:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2027-07-01T12:23:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2027-07-01T12:23:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: End Step	And I select Log out from System power drop down menu
2027-07-01T12:23:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: Start Step	And Clicked on Logout confirmation Yes button
2027-07-01T12:23:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation Yes button	
2027-07-01T12:23:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation YES button is performed	
2027-07-01T12:23:38	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation YES button	
2027-07-01T12:23:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: End Step	And Clicked on Logout confirmation Yes button
2027-07-01T12:23:38	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: Start Step	Then Logon is up and running
2027-07-01T12:23:44	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:23:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T12:23:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T12:23:44	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:23:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: End Step	Then Logon is up and running
2027-07-01T12:23:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: End Scenario	Verify user logout
2027-07-01T12:23:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:23:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:23:44	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T12:23:45	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:23:45.093 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 12:23:45.093 PM
**********************************************************************************

2027-07-01T12:23:45	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130092
AUTID: 30130094
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130094
RUNNERID: 30130095
AUTID: 30130096
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130096
RUNNERID: 30130097
AUTID: 30130098
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130098
2027-07-01T12:23:45	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T12:23:50	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T12:23:56	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T12:23:56	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2027-07-01T12:23:56	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2027-07-01T12:23:56	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2027-07-01T12:23:56	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2027-07-01T12:23:57	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2027-07-01T12:23:57	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2027-07-01T12:24:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2027-07-01T12:24:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2027-07-01T12:24:12	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2027-07-01T12:24:12	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 30130099
AUTID: 30130100
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130100
2027-07-01T12:24:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T12:24:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2027-07-01T12:24:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2027-07-01T12:24:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T12:24:53	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T12:24:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2027-07-01T12:24:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T12:24:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T12:24:53	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T12:24:53	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T12:24:53	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 12:24:53.900 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT_CLICK_CONFIRMATION_NO
TestCase Start Time - 01-07-2027 13:08:23.586 PM
**********************************************************************************

2027-07-01T13:08:23	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30210080
AUTID: 30210082
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30210082
RUNNERID: 30210083
AUTID: 30210084
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30210084
2027-07-01T13:08:23	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: Start Scenario	Verify user logout
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: Start Step	Given PDM is UP
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T13:08:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: End Step	Given PDM is UP
2027-07-01T13:08:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: Start Step	When I clicked on Power Icon
2027-07-01T13:08:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T13:08:30	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T13:08:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: End Step	When I clicked on Power Icon
2027-07-01T13:08:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: Start Step	And I select Log out from System power drop down menu
2027-07-01T13:08:30	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2027-07-01T13:08:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2027-07-01T13:08:32	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2027-07-01T13:08:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: End Step	And I select Log out from System power drop down menu
2027-07-01T13:08:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: Start Step	And Clicked on Logout confirmation No button
2027-07-01T13:08:32	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation No button	
2027-07-01T13:08:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation NO button is performed	
2027-07-01T13:08:33	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation NO button	
2027-07-01T13:08:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: End Step	And Clicked on Logout confirmation No button
2027-07-01T13:08:33	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: Start Step	Then Verify PDM is Up and Running
2027-07-01T13:09:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T13:09:13	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T13:09:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: End Step	Then Verify PDM is Up and Running
2027-07-01T13:09:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: End Scenario	Verify user logout
2027-07-01T13:09:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T13:09:13	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T13:09:13	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T13:09:13	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 13:09:13.955 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT
TestCase Start Time - 01-07-2027 13:09:13.960 PM
**********************************************************************************

2027-07-01T13:09:13	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30210085
AUTID: 30210087
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30210087
RUNNERID: 30210088
AUTID: 30210089
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30210089
RUNNERID: 30210090
AUTID: 30210091
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30210091
2027-07-01T13:09:14	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: Start Scenario	Verify user logout
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: Start Step	Given PDM is UP
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2027-07-01T13:09:19	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: End Step	Given PDM is UP
2027-07-01T13:09:19	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: Start Step	When I clicked on Power Icon
2027-07-01T13:09:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2027-07-01T13:09:20	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2027-07-01T13:09:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: End Step	When I clicked on Power Icon
2027-07-01T13:09:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: Start Step	And I select Log out from System power drop down menu
2027-07-01T13:09:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2027-07-01T13:09:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2027-07-01T13:09:22	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2027-07-01T13:09:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: End Step	And I select Log out from System power drop down menu
2027-07-01T13:09:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: Start Step	And Clicked on Logout confirmation Yes button
2027-07-01T13:09:22	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation Yes button	
2027-07-01T13:09:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation YES button is performed	
2027-07-01T13:09:23	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation YES button	
2027-07-01T13:09:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: End Step	And Clicked on Logout confirmation Yes button
2027-07-01T13:09:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: Start Step	Then Logon is up and running
2027-07-01T13:09:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T13:09:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2027-07-01T13:09:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2027-07-01T13:09:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T13:09:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: End Step	Then Logon is up and running
2027-07-01T13:09:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: End Scenario	Verify user logout
2027-07-01T13:09:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T13:09:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T13:09:29	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T13:09:29	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 13:09:29.726 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2027 13:09:29.726 PM
**********************************************************************************

2027-07-01T13:09:29	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30210092
AUTID: 30210094
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30210094
2027-07-01T13:09:29	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2027-07-01T13:09:35	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2027-07-01T13:09:40	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2027-07-01T13:09:40	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2027-07-01T13:09:40	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Screen shot taken : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/FailedScenarioScreenshots/FailedImage_2027-07-01_13:09:40.PNG	
2027-07-01T13:09:41	ERROR     	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:58: Script Error	RuntimeError: TEST FAILURE :Logon screen is not visible:
2027-07-01T13:09:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2027-07-01T13:09:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Skip Step	When Enter username
2027-07-01T13:09:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Skip Step	And Enter password
2027-07-01T13:09:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Skip Step	When Clicked on Logon button
2027-07-01T13:09:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Skip Step	And Accept Disclaimer
2027-07-01T13:09:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Skip Step	Then Verify PDM is Up and Running
2027-07-01T13:09:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2027-07-01T13:09:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2027-07-01T13:09:41	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2027-07-01T13:09:41	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2027-07-01T13:09:41	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   1
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   1
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2027 13:09:41.699 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT_CLICK_CONFIRMATION_NO
TestCase Start Time - 01-07-2025 13:50:10.711 PM
**********************************************************************************

2025-07-01T13:50:10	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130080
AUTID: 30130082
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130082
RUNNERID: 30130083
AUTID: 30130084
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130084
2025-07-01T13:50:10	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: Start Scenario	Verify user logout
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: Start Step	Given PDM is UP
2025-07-01T13:50:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:50:17	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:50:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:33: End Step	Given PDM is UP
2025-07-01T13:50:17	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: Start Step	When I clicked on Power Icon
2025-07-01T13:50:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2025-07-01T13:50:18	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2025-07-01T13:50:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:34: End Step	When I clicked on Power Icon
2025-07-01T13:50:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: Start Step	And I select Log out from System power drop down menu
2025-07-01T13:50:18	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2025-07-01T13:50:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2025-07-01T13:50:20	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2025-07-01T13:50:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:35: End Step	And I select Log out from System power drop down menu
2025-07-01T13:50:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: Start Step	And Clicked on Logout confirmation No button
2025-07-01T13:50:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation No button	
2025-07-01T13:50:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation NO button is performed	
2025-07-01T13:50:20	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation NO button	
2025-07-01T13:50:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:36: End Step	And Clicked on Logout confirmation No button
2025-07-01T13:50:20	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: Start Step	Then Verify PDM is Up and Running
2025-07-01T13:51:00	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:51:01	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:51:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:37: End Step	Then Verify PDM is Up and Running
2025-07-01T13:51:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:31: End Scenario	Verify user logout
2025-07-01T13:51:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:51:01	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:51:01	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2025-07-01T13:51:01	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   5
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   5
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:51:01.774 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGOUT
TestCase Start Time - 01-07-2025 13:51:01.779 PM
**********************************************************************************

2025-07-01T13:51:01	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130085
AUTID: 30130087
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130087
RUNNERID: 30130088
AUTID: 30130089
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130089
RUNNERID: 30130090
AUTID: 30130091
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130091
2025-07-01T13:51:01	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: Start Scenario	Verify user logout
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: Start Step	Given PDM is UP
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:51:07	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:24: End Step	Given PDM is UP
2025-07-01T13:51:07	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: Start Step	When I clicked on Power Icon
2025-07-01T13:51:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Power Icon is performed	
2025-07-01T13:51:08	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Power Icon	
2025-07-01T13:51:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:25: End Step	When I clicked on Power Icon
2025-07-01T13:51:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: Start Step	And I select Log out from System power drop down menu
2025-07-01T13:51:08	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: I select Log out from System power drop down menu	
2025-07-01T13:51:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Activate Item action is performed on menu item Log out	
2025-07-01T13:51:10	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Log out is selected from the menu Power Icon	
2025-07-01T13:51:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:26: End Step	And I select Log out from System power drop down menu
2025-07-01T13:51:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: Start Step	And Clicked on Logout confirmation Yes button
2025-07-01T13:51:10	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Clicked on Logout confirmation Yes button	
2025-07-01T13:51:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Sysytem Log out confirmation YES button is performed	
2025-07-01T13:51:11	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Sysytem Log out confirmation YES button	
2025-07-01T13:51:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:27: End Step	And Clicked on Logout confirmation Yes button
2025-07-01T13:51:11	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: Start Step	Then Logon is up and running
2025-07-01T13:51:16	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:51:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2025-07-01T13:51:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2025-07-01T13:51:16	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:51:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:28: End Step	Then Logon is up and running
2025-07-01T13:51:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:22: End Scenario	Verify user logout
2025-07-01T13:51:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:51:16	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:51:16	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2025-07-01T13:51:17	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   6
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   6
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:51:17.465 PM
**********************************************************************************



**********************************************************************************
TESTCASE STARTED - 
TestCase Name - tst_User_Logout_Login
Scenario Name - LOGIN_AS_CLINICAL_USER
TestCase Start Time - 01-07-2025 13:51:17.466 PM
**********************************************************************************

2025-07-01T13:51:17	START     	Start 'TestCases'             	Test 'TestCases' started
RUNNERID: 30130092
AUTID: 30130094
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130094
RUNNERID: 30130095
AUTID: 30130096
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130096
RUNNERID: 30130097
AUTID: 30130098
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130098
2025-07-01T13:51:17	START_TEST_CASE	Start 'tst_User_Logout_Login' 	Test 'tst_User_Logout_Login' started (tst_User_Logout_Login)
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: CommonPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: browserPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: navigationBarPageObjects init	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Start of main !	
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: Start Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: Start Scenario	Verify login of clinical user
2025-07-01T13:51:23	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: Start Step	Given Logon is up and running
2025-07-01T13:51:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:51:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object  is found	
2025-07-01T13:51:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is Visible	
2025-07-01T13:51:28	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached LOGON as an AUT	
2025-07-01T13:51:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:42: End Step	Given Logon is up and running
2025-07-01T13:51:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: Start Step	When Enter username
2025-07-01T13:51:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2025-07-01T13:51:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: object Logon page user name text box is found	
2025-07-01T13:51:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object Logon page user name text box is Editable	
2025-07-01T13:51:28	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: action on object Logon page user name text box is performed	
2025-07-01T13:51:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page user name text box	
2025-07-01T13:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:43: End Step	When Enter username
2025-07-01T13:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: Start Step	And Enter password
2025-07-01T13:51:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Given text typed on field Logon page password text box	
2025-07-01T13:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:46: End Step	And Enter password
2025-07-01T13:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: Start Step	When Clicked on Logon button
2025-07-01T13:51:29	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: button click object Logon page Logon button is performed	
2025-07-01T13:51:29	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Button click performed on Logon page Logon button	
2025-07-01T13:51:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:49: End Step	When Clicked on Logon button
2025-07-01T13:51:39	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: Start Step	And Accept Disclaimer
2025-07-01T13:51:44	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Step: Accept Disclaimer	
2025-07-01T13:51:45	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached UserAuth as an AUT	
RUNNERID: 30130099
AUTID: 30130100
AUTHOST: 12*******
AUTPORT: 4322
RUNNERID: -1
application registered server: 12******* 4322 aut: java 30130100
2025-07-01T13:51:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Object  is not Visible	
2025-07-01T13:51:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:50: End Step	And Accept Disclaimer
2025-07-01T13:51:45	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: Start Step	Then Verify PDM is Up and Running
2025-07-01T13:52:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: PDM is Up and Running	
2025-07-01T13:52:25	PASS      	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:69: TEST PASSED :Attached PDM as an AUT	
2025-07-01T13:52:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:51: End Step	Then Verify PDM is Up and Running
2025-07-01T13:52:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:40: End Scenario	Verify login of clinical user
2025-07-01T13:52:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/tst_User_Logout_Login/test.feature:1: End Feature	USER_LOGOUT_LOGIN : The purpose of this feature is to perform logout and login steps
2025-07-01T13:52:25	LOG       	/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/Architecture/AutomationInterfaces/Common/Logger.py:21: Exiting main !	
2025-07-01T13:52:25	END_TEST_CASE	End 'tst_User_Logout_Login'   	End of test 'tst_User_Logout_Login'
2025-07-01T13:52:26	END       	End 'TestCases'               	End of test 'TestCases'
*******************************************************
Summary:
Number of Test Cases:	   1
Number of Tests:	   7
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
Number of Passes:	   7
Number of Expected Fails:	   0
Number of Unexpected Passes:	   0
Number of Warnings:	   0
*******************************************************

**********************************************************************************
TESTCASE COMPLETED
TestCase End Time - 01-07-2025 13:52:26.442 PM
**********************************************************************************


