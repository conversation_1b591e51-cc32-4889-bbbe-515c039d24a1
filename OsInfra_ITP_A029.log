USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A029 ***************

Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2027 07:35:57.561 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3019

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN_CLICK_NO
Time taken to execute script 8864 ms
Current Time : 01-07-2027 07:36:06.461 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click on Shutdown and to Click No on Confirmation pop up
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_System_Shutdown:SHUTDOWN_CLICK_NO,tst_System_Shutdown:SHUTDOWN
InputAcceptableLastRebootTimeDiffInSecs = 300
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click on Shutdown and to Click No on Confirmation pop up 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A029 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A029 ***************

Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2027 10:09:12.641 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN_CLICK_NO
Time taken to execute script 11985 ms
Current Time : 01-07-2027 10:09:24.662 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Shutdown, Confirmation pop up appeared ,Clicked on No successfuly 

wait for 30 sec to verify if shutdown happens when clicked on No....

Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "Shutdown" | cut -f1-3 -d ':'
Time taken to execute script 15 ms

Last Shutdown Time found empty
system Shutdown was not done when Clicked No on Confirmation pop up
Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2027 10:09:54.678 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 16 ms

Squish Server is already running with pid 2801

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN
Time taken to execute script 12059 ms
Current Time : 01-07-2027 10:10:06.754 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Shutdown, Confirmation pop up appeared ,Clicked on Yes successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_System_Shutdown:SHUTDOWN_CLICK_NO,tst_System_Shutdown:SHUTDOWN
InputAcceptableLastRebootTimeDiffInSecs = 300
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Shutdown, Confirmation pop up appeared ,Clicked on No successfuly 
system Shutdown was not done when Clicked No on Confirmation pop up
 Clicked on Shutdown, Confirmation pop up appeared ,Clicked on Yes successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A029 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A029 ***************

Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2027 10:45:55.531 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 2980

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN_CLICK_NO
Time taken to execute script 8842 ms
Current Time : 01-07-2027 10:46:04.409 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click on Shutdown and to Click No on Confirmation pop up
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_System_Shutdown:SHUTDOWN_CLICK_NO,tst_System_Shutdown:SHUTDOWN
InputAcceptableLastRebootTimeDiffInSecs = 300
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click on Shutdown and to Click No on Confirmation pop up 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A029 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A029 ***************

Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2027 11:54:03.574 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN_CLICK_NO
Time taken to execute script 12022 ms
Current Time : 01-07-2027 11:54:15.631 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Shutdown, Confirmation pop up appeared ,Clicked on No successfuly 

wait for 30 sec to verify if shutdown happens when clicked on No....

Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "Shutdown" | cut -f1-3 -d ':'
Time taken to execute script 14 ms
Current Time : 2027-07-01,11:54:45

Calculating time difference

Start Time : 2027-07-01,10:10:05
End time : 2027-07-01,11:54:45

Time difference in seconds : 6280

Expected Time Difference In Seconds : 300

Actual Time Difference In Seconds : 6280

Unexpected shutdown time difference
system Shutdown was not done when Clicked No on Confirmation pop up
Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2027 11:54:45.653 AM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 22 ms

Squish Server is already running with pid 3046

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN
Time taken to execute script 12057 ms
Current Time : 01-07-2027 11:54:57.732 AM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Shutdown, Confirmation pop up appeared ,Clicked on Yes successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_System_Shutdown:SHUTDOWN_CLICK_NO,tst_System_Shutdown:SHUTDOWN
InputAcceptableLastRebootTimeDiffInSecs = 300
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Shutdown, Confirmation pop up appeared ,Clicked on No successfuly 
system Shutdown was not done when Clicked No on Confirmation pop up
 Clicked on Shutdown, Confirmation pop up appeared ,Clicked on Yes successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A029 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A029 ***************

Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2027 12:26:18.126 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 29 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN_CLICK_NO
Time taken to execute script 11923 ms
Current Time : 01-07-2027 12:26:30.085 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Shutdown, Confirmation pop up appeared ,Clicked on No successfuly 

wait for 30 sec to verify if shutdown happens when clicked on No....

Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "Shutdown" | cut -f1-3 -d ':'
Time taken to execute script 14 ms
Current Time : 2027-07-01,12:27:00

Calculating time difference

Start Time : 2027-07-01,11:54:56
End time : 2027-07-01,12:27:00

Time difference in seconds : 1924

Expected Time Difference In Seconds : 300

Actual Time Difference In Seconds : 1924

Unexpected shutdown time difference
system Shutdown was not done when Clicked No on Confirmation pop up
Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2027 12:27:00.103 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 20 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN
Time taken to execute script 11909 ms
Current Time : 01-07-2027 12:27:12.034 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Shutdown, Confirmation pop up appeared ,Clicked on Yes successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_System_Shutdown:SHUTDOWN_CLICK_NO,tst_System_Shutdown:SHUTDOWN
InputAcceptableLastRebootTimeDiffInSecs = 300
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Shutdown, Confirmation pop up appeared ,Clicked on No successfuly 
system Shutdown was not done when Clicked No on Confirmation pop up
 Clicked on Shutdown, Confirmation pop up appeared ,Clicked on Yes successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A029 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A029 ***************

Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2027 13:09:47.980 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3021

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN_CLICK_NO
Time taken to execute script 30472 ms
Current Time : 01-07-2027 13:10:18.488 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   1
Number of Fatals:	   0
Number of Fails:	   0
Failed to Click on Shutdown and to Click No on Confirmation pop up
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_System_Shutdown:SHUTDOWN_CLICK_NO,tst_System_Shutdown:SHUTDOWN
InputAcceptableLastRebootTimeDiffInSecs = 300
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Failed to Click on Shutdown and to Click No on Confirmation pop up 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A029 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A029 ***************

Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2025 13:53:50.575 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 28 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN_CLICK_NO
Time taken to execute script 12000 ms
Current Time : 01-07-2025 13:54:02.610 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Shutdown, Confirmation pop up appeared ,Clicked on No successfuly 

wait for 30 sec to verify if shutdown happens when clicked on No....

Executing : bash -c tac /export/logfiles/senovision/error.log | grep -m 1 "Shutdown" | cut -f1-3 -d ':'
Time taken to execute script 17 ms
Current Time : 2025-07-01,13:54:32

Calculating time difference

Start Time : 2027-07-01,12:27:11
End time : 2025-07-01,13:54:32

Time difference in seconds : 1357750

Expected Time Difference In Seconds : 300

Actual Time Difference In Seconds : 1357750

Unexpected shutdown time difference
system Shutdown was not done when Clicked No on Confirmation pop up
Clicking on Shutdown from power dropdown menu
Current Time : 01-07-2025 13:54:32.630 PM

Checking if squish server is already running

Executing : /usr/bin/pgrep squishserver
Time taken to execute script 13 ms

Squish Server is already running with pid 3013

Starting squish runner for the testcase

Executing : /export/home1/axis_data/PDMAutomation/squish-6.0.0-java-linux64/bin/squishrunner --host 127.0.0.1 --port 4322 --testsuite /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/ --testcase tst_System_Shutdown --tags @SHUTDOWN
Time taken to execute script 12000 ms
Current Time : 01-07-2025 13:54:44.644 PM
Output Folder : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration
Output File With Path : /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/logs/Integration/OsInfra_ITP_A029_SquishRunner.log
Number of Errors:	   0
Number of Fatals:	   0
Number of Fails:	   0
All steps are passed
Clicked on Shutdown, Confirmation pop up appeared ,Clicked on Yes successfuly 
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputSquishTestCaseSequence = tst_System_Shutdown:SHUTDOWN_CLICK_NO,tst_System_Shutdown:SHUTDOWN
InputAcceptableLastRebootTimeDiffInSecs = 300
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Clicked on Shutdown, Confirmation pop up appeared ,Clicked on No successfuly 
system Shutdown was not done when Clicked No on Confirmation pop up
 Clicked on Shutdown, Confirmation pop up appeared ,Clicked on Yes successfuly 
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A029 ***************

Test Result : SUCCESS
