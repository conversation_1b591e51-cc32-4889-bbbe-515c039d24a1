USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A035 ***************

Executing command : bash -c logger -t posevents bbbbb

Executing : bash -c who -b | awk '{print $3" "$4}'
Time taken to execute script 5 ms


System Reboot Time : 2027-07-01 10:17


Expected ownership : sdc sdc

Executing : bash -c find /export/home/<USER>/senovision/logfiles/pos/ -name "postraces*" -newermt "2027-07-01 10:17" -ls | awk '{print $5" "$6}' | tail -1
Time taken to execute script 4 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/:postraces* is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c find /export/home/<USER>/senovision/logfiles/ -name "PoseidonEvents.log*" -newermt "2027-07-01 10:17" -ls | awk '{print $5" "$6}' | tail -1
Time taken to execute script 6 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/:PoseidonEvents.log* is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c find /var/log/ -name "syslog*" -newermt "2027-07-01 10:17" -ls | awk '{print $5" "$6}' | tail -1
Time taken to execute script 8 ms


Actual ownership : root root

File /var/log/:syslog* is found with ownership root root
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/:postraces*,/export/home/<USER>/senovision/logfiles/:PoseidonEvents.log*,/var/log/:syslog*
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

File /export/home/<USER>/senovision/logfiles/pos/:postraces* is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/:PoseidonEvents.log* is found with ownership sdc sdc
File /var/log/:syslog* is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A035 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A035 ***************

Executing command : bash -c logger -t posevents bbbbb

Executing : bash -c who -b | awk '{print $3" "$4}'
Time taken to execute script 4 ms


System Reboot Time : 2027-07-01 12:37


Expected ownership : sdc sdc

Executing : bash -c find /export/home/<USER>/senovision/logfiles/pos/ -name "postraces*" -newermt "2027-07-01 12:37" -ls | awk '{print $5" "$6}' | tail -1
Time taken to execute script 4 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/:postraces* is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c find /export/home/<USER>/senovision/logfiles/ -name "PoseidonEvents.log*" -newermt "2027-07-01 12:37" -ls | awk '{print $5" "$6}' | tail -1
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/:PoseidonEvents.log* is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c find /var/log/ -name "syslog*" -newermt "2027-07-01 12:37" -ls | awk '{print $5" "$6}' | tail -1
Time taken to execute script 7 ms


Actual ownership : root root

File /var/log/:syslog* is found with ownership root root
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/:postraces*,/export/home/<USER>/senovision/logfiles/:PoseidonEvents.log*,/var/log/:syslog*
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

File /export/home/<USER>/senovision/logfiles/pos/:postraces* is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/:PoseidonEvents.log* is found with ownership sdc sdc
File /var/log/:syslog* is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A035 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A035 ***************

Executing command : bash -c logger -t posevents bbbbb

Executing : bash -c who -b | awk '{print $3" "$4}'
Time taken to execute script 4 ms


System Reboot Time : 2025-07-01 14:04


Expected ownership : sdc sdc

Executing : bash -c find /export/home/<USER>/senovision/logfiles/pos/ -name "postraces*" -newermt "2025-07-01 14:04" -ls | awk '{print $5" "$6}' | tail -1
Time taken to execute script 5 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/pos/:postraces* is found with ownership sdc sdc


Expected ownership : sdc sdc

Executing : bash -c find /export/home/<USER>/senovision/logfiles/ -name "PoseidonEvents.log*" -newermt "2025-07-01 14:04" -ls | awk '{print $5" "$6}' | tail -1
Time taken to execute script 4 ms


Actual ownership : sdc sdc

File /export/home/<USER>/senovision/logfiles/:PoseidonEvents.log* is found with ownership sdc sdc


Expected ownership : root root

Executing : bash -c find /var/log/ -name "syslog*" -newermt "2025-07-01 14:04" -ls | awk '{print $5" "$6}' | tail -1
Time taken to execute script 5 ms


Actual ownership : root root

File /var/log/:syslog* is found with ownership root root
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputLogPosEvents = logger -t posevents bbbbb
InputLogFilesToCheck = /export/home/<USER>/senovision/logfiles/pos/:postraces*,/export/home/<USER>/senovision/logfiles/:PoseidonEvents.log*,/var/log/:syslog*
ExpectedOwnership = sdc sdc,sdc sdc,root root
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

File /export/home/<USER>/senovision/logfiles/pos/:postraces* is found with ownership sdc sdc
File /export/home/<USER>/senovision/logfiles/:PoseidonEvents.log* is found with ownership sdc sdc
File /var/log/:syslog* is found with ownership root root
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A035 ***************

Test Result : SUCCESS
