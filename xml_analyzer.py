#!/usr/bin/env python3
"""
XML Test Results Analy<PERSON> Script
Converts FinalResult.xml test results into a beautiful HTML report with charts and visualizations.
Analyzes JUnit-style XML test result files.
"""

import os
import xml.etree.ElementTree as ET
import json
from datetime import datetime
from collections import defaultdict, Counter
import glob
import re

class XMLAnalyzer:
    def __init__(self, xml_directory="/export/home1/axis_data/SATS/"):
        self.xml_directory = xml_directory
        self.test_results = []
        self.summary_stats = {
            'total_files': 0,
            'total_tests': 0,
            'total_passes': 0,
            'total_failures': 0,
            'total_errors': 0,
            'total_skipped': 0,
            'success_rate': 0.0,
            'total_time': 0.0
        }
        self.testsuite_info = {}
    
    def parse_xml_files(self):
        """Parse XML test result files (FinalResult.xml)"""
        pattern = os.path.join(self.xml_directory, "FinalResult.xml")
        xml_files = glob.glob(pattern)
        
        # Also look for any XML files that might contain test results
        if not xml_files:
            pattern = os.path.join(self.xml_directory, "*.xml")
            xml_files = glob.glob(pattern)
        
        print(f"Debug: Pattern used: {pattern}")
        print(f"Debug: XML files found: {xml_files}")
        print(f"Found {len(xml_files)} XML files")
        
        for xml_file in xml_files:
            print(f"Processing XML file: {xml_file}")
            
            try:
                # First, try to fix common XML issues
                fixed_content = self._fix_xml_content(xml_file)
                
                if fixed_content:
                    # Parse the fixed content
                    root = ET.fromstring(fixed_content)
                else:
                    # Fall back to normal parsing
                    tree = ET.parse(xml_file)
                    root = tree.getroot()
                
                # Handle different XML structures
                if root.tag == 'testsuites':
                    self._parse_testsuites(root, xml_file)
                elif root.tag == 'testsuite':
                    self._parse_testsuite(root, xml_file)
                else:
                    print(f"Unknown XML format in {xml_file}: root tag is {root.tag}")
                
            except ET.ParseError as e:
                print(f"Error parsing XML {xml_file}: {e}")
                print(f"Attempting to parse as text-based format...")
                self._parse_as_text(xml_file)
            except Exception as e:
                print(f"Error processing {xml_file}: {e}")
                print(f"Attempting to parse as text-based format...")
                self._parse_as_text(xml_file)
    
    def _parse_testsuites(self, root, xml_file):
        """Parse testsuites element (contains multiple testsuite elements)"""
        for testsuite in root.findall('testsuite'):
            self._parse_testsuite(testsuite, xml_file)
    
    def _parse_testsuite(self, testsuite, xml_file):
        """Parse a testsuite element"""
        testsuite_name = testsuite.get('name', 'Unknown')
        testsuite_tests = testsuite.get('tests', '0')
        testsuite_failures = testsuite.get('failures', '0')
        testsuite_errors = testsuite.get('errors', '0')
        testsuite_time = testsuite.get('time', '0')
        
        print(f"  Processing testsuite: {testsuite_name}")
        print(f"  Tests: {testsuite_tests}, Failures: {testsuite_failures}, Errors: {testsuite_errors}")
        
        # Store testsuite info
        self.testsuite_info[testsuite_name] = {
            'tests': testsuite_tests,
            'failures': testsuite_failures,
            'errors': testsuite_errors,
            'time': testsuite_time
        }
        
        # Parse individual test cases
        for testcase in testsuite.findall('testcase'):
            self._parse_testcase(testcase, testsuite_name, xml_file)
    
    def _parse_testcase(self, testcase, testsuite_name, xml_file):
        """Parse a testcase element"""
        test_name = testcase.get('name', 'Unknown')
        classname = testcase.get('classname', 'Unknown')
        time = float(testcase.get('time', '0'))
        
        # Determine status and extract error/failure information
        status = 'PASS'
        error_message = ''
        failure_message = ''
        
        # Check for failure
        failure_elem = testcase.find('failure')
        if failure_elem is not None:
            status = 'FAIL'
            failure_message = failure_elem.get('message', '')
            failure_text = failure_elem.text or ''
            
            # Extract meaningful error message
            if failure_text.strip():
                # Try to extract the actual failure reason
                failure_lines = failure_text.strip().split('\n')
                for line in failure_lines:
                    if 'Test Failed:' in line:
                        error_message = line.split('Test Failed:')[-1].strip()
                        break
                    elif 'Failed:' in line:
                        error_message = line.split('Failed:')[-1].strip()
                        break
                
                if not error_message:
                    error_message = failure_text.strip()[:200]  # First 200 chars
            
            if not error_message and failure_message:
                error_message = failure_message
        
        # Check for error
        error_elem = testcase.find('error')
        if error_elem is not None:
            status = 'ERROR'
            error_message = error_elem.get('message', '')
            error_text = error_elem.text or ''
            
            # Extract meaningful error message
            if error_text.strip():
                # Try to extract the actual error reason
                error_lines = error_text.strip().split('\n')
                for line in error_lines:
                    if 'Test Failed:' in line:
                        error_message = line.split('Test Failed:')[-1].strip()
                        break
                    elif 'Failed:' in line:
                        error_message = line.split('Failed:')[-1].strip()
                        break
                
                if not error_message:
                    error_message = error_text.strip()[:200]  # First 200 chars
        
        # Check for skipped
        skipped_elem = testcase.find('skipped')
        if skipped_elem is not None:
            status = 'SKIPPED'
            error_message = skipped_elem.get('message', 'Test was skipped')
        
        # Check system-out for additional status info
        system_out = testcase.find('system-out')
        if system_out is not None and system_out.text:
            system_out_text = system_out.text.strip()
            if system_out_text == 'TEST_PASSED':
                status = 'PASS'
            elif system_out_text == 'TEST_FAILED':
                if status == 'PASS':  # Only change if not already set to FAIL/ERROR
                    status = 'FAIL'
        
        # Clean up the class name for display
        display_classname = classname.split('.')[-1] if '.' in classname else classname
        
        # Create a more readable description
        description = f"{display_classname}.{test_name}"
        
        self.test_results.append({
            'test_name': test_name,
            'classname': classname,
            'display_classname': display_classname,
            'description': description,
            'testsuite': testsuite_name,
            'status': status,
            'time': time,
            'error_message': error_message,
            'failure_message': failure_message,
            'file_path': xml_file
        })
        
        print(f"    Test: {test_name} - Status: {status} - Time: {time}s")
        if error_message:
            print(f"      Error: {error_message[:100]}...")
    
    def _fix_xml_content(self, xml_file):
        """Try to fix common XML issues by reading and cleaning the file"""
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Common XML fixes
            # Remove invalid characters
            content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)
            
            # Fix common encoding issues
            content = content.replace('&', '&amp;')
            content = content.replace('<', '&lt;')
            content = content.replace('>', '&gt;')
            
            # Restore XML tags
            content = re.sub(r'&lt;(\/?[a-zA-Z][^&]*?)&gt;', r'<\1>', content)
            
            # Try to parse the fixed content
            try:
                ET.fromstring(content)
                return content
            except ET.ParseError:
                return None
                
        except Exception as e:
            print(f"Error fixing XML content: {e}")
            return None
    
    def _parse_as_text(self, xml_file):
        """Parse XML-like content as text when XML parsing fails"""
        try:
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for test patterns in the text
            # This is a fallback parser for malformed XML
            print(f"Attempting text-based parsing of {xml_file}")
            
            # Try to extract test information from text patterns
            test_patterns = [
                r'<testcase[^>]*name="([^"]*)"[^>]*>',
                r'test[_\s]*name[:\s]*([^\n\r]*)',
                r'Test[:\s]*([^\n\r]*)'
            ]
            
            found_tests = []
            for pattern in test_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                found_tests.extend(matches)
            
            if found_tests:
                print(f"Found {len(found_tests)} potential test cases in text format")
                for test_name in found_tests[:10]:  # Limit to first 10 for display
                    # Create a basic test result entry
                    self.test_results.append({
                        'test_name': test_name.strip(),
                        'classname': 'TextParsed',
                        'display_classname': 'TextParsed',
                        'description': f"TextParsed.{test_name.strip()}",
                        'testsuite': 'TextBased',
                        'status': 'UNKNOWN',
                        'time': 0.0,
                        'error_message': 'Parsed from text format',
                        'failure_message': '',
                        'file_path': xml_file
                    })
            else:
                print("No test patterns found in text format")
                
        except Exception as e:
            print(f"Error parsing as text: {e}")
    
    def calculate_summary_stats(self):
        """Calculate overall summary statistics"""
        self.summary_stats['total_files'] = len(set(result['file_path'] for result in self.test_results))
        self.summary_stats['total_tests'] = len(self.test_results)
        
        status_counts = Counter(result['status'] for result in self.test_results)
        
        self.summary_stats['total_passes'] = status_counts.get('PASS', 0)
        self.summary_stats['total_failures'] = status_counts.get('FAIL', 0)
        self.summary_stats['total_errors'] = status_counts.get('ERROR', 0)
        self.summary_stats['total_skipped'] = status_counts.get('SKIPPED', 0)
        
        # Calculate success rate
        if self.summary_stats['total_tests'] > 0:
            self.summary_stats['success_rate'] = (self.summary_stats['total_passes'] / self.summary_stats['total_tests']) * 100
        
        # Calculate total time
        self.summary_stats['total_time'] = sum(result['time'] for result in self.test_results)
    
    def analyze_xml_files(self):
        """Main method to analyze all XML files"""
        print(f"Starting XML analysis in directory: {self.xml_directory}")
        
        # Check if directory exists
        if not os.path.exists(self.xml_directory):
            print(f"Error: Directory {self.xml_directory} does not exist!")
            print("Please check the path or update the xml_directory parameter.")
            return None, []
        
        self.parse_xml_files()
        self.calculate_summary_stats()
        print(f"Analysis complete. Found {len(self.test_results)} test cases.")
        return self.summary_stats, self.test_results

def generate_html_report(summary_stats, test_results, output_file="sats_test_report.html"):
    """Generate a ultra-modern beautiful HTML report with elegant charts"""
    
    if not summary_stats:
        print("No data to generate report")
        return
    
    # Prepare data for charts
    status_data = {
        'passes': summary_stats['total_passes'],
        'failures': summary_stats['total_failures'],
        'errors': summary_stats['total_errors'],
        'skipped': summary_stats['total_skipped']
    }
    
    # Test suite distribution
    testsuite_counts = Counter(result['testsuite'] for result in test_results)
    testsuite_data = dict(testsuite_counts.most_common(10))  # Top 10 test suites
    
    # Class distribution
    class_counts = Counter(result['display_classname'] for result in test_results)
    class_data = dict(class_counts.most_common(10))  # Top 10 classes
    
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SATS Test Execution Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {{
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --purple-color: #8b5cf6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }}

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
            color: var(--text-primary);
        }}
        
        .dashboard {{
            max-width: 1200px;
            margin: 0 auto;
            background: var(--bg-primary);
            border-radius: 24px;
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }}
        
        .header {{
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: white;
            padding: 1rem 2rem;
            position: relative;
            overflow: hidden;
        }}
        
        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }}
        
        @keyframes shimmer {{
            0%, 100% {{ transform: translateX(-100%); }}
            50% {{ transform: translateX(100%); }}
        }}
        
        .header-content {{
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .header h1 {{
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            background: linear-gradient(45deg, #ffffff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .header .subtitle {{
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 400;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }}
        
        .stats-overview {{
            padding: 0.25rem;
            background: var(--bg-secondary);
        }}
        
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }}
        
        .stat-card {{
            background: var(--bg-primary);
            padding: 0.75rem;
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }}
        
        .stat-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-accent, var(--info-color));
            transition: height 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
        }}
        
        .stat-card:hover::before {{
            height: 8px;
        }}
        
        .stat-card.success {{ --card-accent: var(--success-color); }}
        .stat-card.error {{ --card-accent: var(--error-color); }}
        .stat-card.info {{ --card-accent: var(--info-color); }}
        .stat-card.warning {{ --card-accent: var(--warning-color); }}
        .stat-card.purple {{ --card-accent: var(--purple-color); }}
        
        .stat-header {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }}
        
        .stat-icon {{
            width: 28px;
            height: 28px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            color: white;
            background: var(--card-accent, var(--info-color));
        }}
        
        .stat-number {{
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--card-accent, var(--info-color));
            line-height: 1;
        }}
        
        .stat-label {{
            color: var(--text-secondary);
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-top: 0.5rem;
        }}
        
        .charts-section {{
            padding: 0.25rem;
            background: var(--bg-primary);
        }}
        
        .charts-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-bottom: 0.25rem;
        }}
        
        @media (max-width: 1024px) {{
            .charts-grid {{
                grid-template-columns: 1fr;
            }}
        }}
        
        .chart-container {{
            background: var(--bg-primary);
            padding: 1rem;
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            position: relative;
            height: 250px;
            display: flex;
            flex-direction: column;
        }}
        
        .chart-header {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            flex-shrink: 0;
        }}
        
        .chart-title {{
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }}
        
        .chart-wrapper {{
            position: relative;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 180px;
        }}
        
        .chart-canvas {{
            max-height: 180px !important;
            max-width: 100% !important;
        }}
        
        .table-section {{
            padding: 1rem;
            background: var(--bg-secondary);
        }}
        
        .section-header {{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }}
        
        .section-title {{
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }}
        
        .table-container {{
            background: var(--bg-primary);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
        }}
        
        .table-wrapper {{
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
        }}
        
        th {{
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            color: white;
            padding: 0.75rem 0.5rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        
        td {{
            padding: 0.75rem 0.5rem;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.85rem;
            vertical-align: top;
        }}
        
        tr:hover {{
            background: var(--bg-secondary);
        }}
        
        tr:last-child td {{
            border-bottom: none;
        }}
        
        .status-badge {{
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }}
        
        .status-pass {{
            background: #dcfce7;
            color: #166534;
        }}
        
        .status-fail {{
            background: #fee2e2;
            color: #991b1b;
        }}
        
        .status-error {{
            background: #fef3c7;
            color: #92400e;
        }}
        
        .status-skipped {{
            background: #f3e8ff;
            color: #6b21a8;
        }}
        
        .error-message {{
            max-width: 300px;
            word-wrap: break-word;
            font-size: 0.8rem;
            color: var(--text-secondary);
            line-height: 1.4;
        }}
        
        .test-name {{
            font-weight: 600;
            color: var(--text-primary);
        }}
        
        .class-name {{
            color: var(--text-secondary);
            font-size: 0.8rem;
            font-family: 'Courier New', monospace;
        }}
        
        .time-badge {{
            background: #f0f9ff;
            color: #0369a1;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 500;
        }}
        
        .footer {{
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            text-align: center;
            padding: 2rem;
            font-size: 0.875rem;
            opacity: 0.9;
        }}
        
        .empty-state {{
            text-align: center;
            padding: 3rem;
            color: var(--text-secondary);
        }}
        
        .empty-state i {{
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }}
        
        @media (max-width: 1200px) {{
            .stats-grid {{
                grid-template-columns: repeat(3, 1fr);
                gap: 1rem;
            }}
        }}
        
        @media (max-width: 768px) {{
            .stats-grid {{
                grid-template-columns: repeat(2, 1fr);
            }}
            
            .header-content {{
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }}
            
            .header h1 {{
                font-size: 1.75rem;
            }}
            
            .dashboard {{
                border-radius: 12px;
                margin: 0.5rem;
            }}
        }}
        
        @media (max-width: 480px) {{
            .stats-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <div class="header-content">
                <h1><i class="fas fa-flask"></i> SATS Test Results</h1>
                <p class="subtitle">
                    <i class="fas fa-calendar-alt"></i>
                    Generated on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}
                </p>
            </div>
        </div>

        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-card info">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-vial"></i>
                        </div>
                        <div class="stat-number">{summary_stats['total_tests']}</div>
                    </div>
                    <div class="stat-label">Total Tests</div>
                </div>
                
                <div class="stat-card success">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number">{summary_stats['total_passes']}</div>
                    </div>
                    <div class="stat-label">Passed Tests</div>
                </div>
                
                <div class="stat-card error">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-number">{summary_stats['total_failures']}</div>
                    </div>
                    <div class="stat-label">Failed Tests</div>
                </div>
                
                <div class="stat-card warning">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-number">{summary_stats['total_errors']}</div>
                    </div>
                    <div class="stat-label">Error Tests</div>
                </div>
                
                <div class="stat-card purple">
                    <div class="stat-header">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-number">{summary_stats['success_rate']:.1f}%</div>
                    </div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>
        </div>

        <div class="charts-section">
            <div class="charts-grid">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-chart-pie"></i>
                            Test Results Overview
                        </h3>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="resultsChart" class="chart-canvas"></canvas>
                    </div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-chart-bar"></i>
                            Top Test Classes
                        </h3>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="classChart" class="chart-canvas"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-section">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-table"></i>
                    Test Results Details
                </h2>
            </div>
            <div class="table-container">
                <div class="table-wrapper">
                    <table>
                        <thead>
                            <tr>
                                <th><i class="fas fa-tag"></i> Test Name</th>
                                <th><i class="fas fa-layer-group"></i> Class</th>
                                <th><i class="fas fa-folder"></i> Suite</th>
                                <th><i class="fas fa-traffic-light"></i> Status</th>
                                <th><i class="fas fa-clock"></i> Time</th>
                                <th><i class="fas fa-exclamation-triangle"></i> Error Message</th>
                            </tr>
                        </thead>
                        <tbody>"""

    # Add test results
    if not test_results:
        html_content += """
                            <tr>
                                <td colspan="6" class="empty-state">
                                    <i class="fas fa-inbox"></i>
                                    <p>No test results found</p>
                                </td>
                            </tr>"""
    else:
        for test in test_results:
            status_class = {
                'PASS': 'status-pass',
                'FAIL': 'status-fail',
                'ERROR': 'status-error',
                'SKIPPED': 'status-skipped'
            }.get(test['status'], 'status-pass')
            
            error_display = test.get('error_message', '') if test['status'] not in ['PASS'] else ''
            
            html_content += f"""
                            <tr>
                                <td>
                                    <div class="test-name">{test['test_name']}</div>
                                </td>
                                <td>
                                    <div class="class-name">{test['display_classname']}</div>
                                </td>
                                <td><strong>{test['testsuite']}</strong></td>
                                <td><span class="status-badge {status_class}">{test['status']}</span></td>
                                <td><span class="time-badge">{test['time']:.2f}s</span></td>
                                <td>
                                    <div class="error-message">{error_display}</div>
                                </td>
                            </tr>"""

    html_content += f"""
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><i class="fas fa-chart-line"></i> Dashboard generated by XML Results Analyzer | <strong>{summary_stats['total_tests']}</strong> tests processed in <strong>{summary_stats['total_time']:.1f}s</strong></p>
        </div>
    </div>

    <script>
        // Modern Chart.js configuration
        const chartDefaults = {{
            responsive: true,
            maintainAspectRatio: false,
            plugins: {{
                legend: {{
                    position: 'bottom',
                    labels: {{
                        padding: 15,
                        font: {{
                            family: 'Inter',
                            size: 12,
                            weight: '500'
                        }},
                        usePointStyle: true,
                        pointStyle: 'circle'
                    }}
                }},
                tooltip: {{
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    cornerRadius: 8,
                    displayColors: false,
                    titleFont: {{
                        family: 'Inter',
                        weight: '600'
                    }},
                    bodyFont: {{
                        family: 'Inter'
                    }}
                }}
            }}
        }};

        // Results Overview Doughnut Chart
        const resultsCtx = document.getElementById('resultsChart').getContext('2d');
        new Chart(resultsCtx, {{
            type: 'doughnut',
            data: {{
                labels: ['Passed', 'Failed', 'Error', 'Skipped'],
                datasets: [{{
                    data: [{status_data['passes']}, {status_data['failures']}, {status_data['errors']}, {status_data['skipped']}],
                    backgroundColor: [
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(139, 92, 246, 0.8)'
                    ],
                    borderColor: [
                        'rgb(16, 185, 129)',
                        'rgb(239, 68, 68)',
                        'rgb(245, 158, 11)',
                        'rgb(139, 92, 246)'
                    ],
                    borderWidth: 2,
                    hoverOffset: 8,
                    cutout: '60%'
                }}]
            }},
            options: {{
                ...chartDefaults,
                plugins: {{
                    ...chartDefaults.plugins,
                    legend: {{
                        ...chartDefaults.plugins.legend,
                        display: true
                    }}
                }}
            }}
        }});

        // Top Classes Bar Chart
        const classCtx = document.getElementById('classChart').getContext('2d');
        const classLabels = {list(class_data.keys())};
        const classValues = {list(class_data.values())};
        
        new Chart(classCtx, {{
            type: 'bar',
            data: {{
                labels: classLabels,
                datasets: [{{
                    label: 'Number of Tests',
                    data: classValues,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: 'rgb(59, 130, 246)',
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false,
                }}]
            }},
            options: {{
                ...chartDefaults,
                plugins: {{
                    ...chartDefaults.plugins,
                    legend: {{
                        display: false
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        ticks: {{
                            stepSize: 1,
                            font: {{
                                family: 'Inter',
                                size: 11
                            }},
                            color: '#6b7280'
                        }},
                        grid: {{
                            color: 'rgba(229, 231, 235, 0.5)'
                        }}
                    }},
                    x: {{
                        ticks: {{
                            font: {{
                                family: 'Inter',
                                size: 10,
                                weight: '500'
                            }},
                            color: '#374151',
                            maxRotation: 45,
                            minRotation: 45
                        }},
                        grid: {{
                            display: false
                        }}
                    }}
                }}
            }}
        }});
        
        // Add smooth animations
        document.querySelectorAll('.stat-card').forEach((card, index) => {{
            card.style.animationDelay = `${{index * 0.1}}s`;
            card.style.animation = 'fadeInUp 0.6s ease-out forwards';
        }});
        
        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {{
                from {{
                    opacity: 0;
                    transform: translateY(30px);
                }}
                to {{
                    opacity: 1;
                    transform: translateY(0);
                }}
            }}
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>"""

    # Save HTML report
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(html_content)

    print(f"âœ¨ Ultra-modern XML HTML report generated: {output_file}")
    return html_content

if __name__ == "__main__":
    # You can change the directory path here if needed
    xml_dir = "/export/home1/axis_data/SATS/"
    
    # For testing locally, you can use current directory
    if not os.path.exists(xml_dir):
        xml_dir = "."
        print(f"Using current directory: {os.path.abspath(xml_dir)}")
    
    # Debug: List all files in the directory
    print(f"Files in directory {xml_dir}:")
    try:
        all_files = os.listdir(xml_dir)
        xml_files = [f for f in all_files if f.endswith('.xml')]
        print(f"  Total files: {len(all_files)}")
        print(f"  XML files: {len(xml_files)}")
        for xml_file in xml_files:
            print(f"    - {xml_file}")
    except Exception as e:
        print(f"Error listing files: {e}")
    
    analyzer = XMLAnalyzer(xml_dir)
    summary_stats, test_results = analyzer.analyze_xml_files()
    
    if summary_stats:
        print(f"\nSummary Statistics:")
        print(f"- Total Tests: {summary_stats['total_tests']}")
        print(f"- Total Passes: {summary_stats['total_passes']}")
        print(f"- Total Failures: {summary_stats['total_failures']}")
        print(f"- Total Errors: {summary_stats['total_errors']}")
        print(f"- Total Skipped: {summary_stats['total_skipped']}")
        print(f"- Success Rate: {summary_stats['success_rate']:.1f}%")
        print(f"- Total Time: {summary_stats['total_time']:.1f}s")

        # Generate HTML report
        generate_html_report(summary_stats, test_results)
        print("\nâœ… HTML report generated successfully!")
        print("ðŸ“„ Open 'sats_test_report.html' in your browser to view the report.")
    else:
        print("No XML files found or analysis failed.")
