USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7109
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7109"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 07:30:26"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7109"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7109
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20270701-073036.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 13 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7312
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7312"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 10:01:10"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7312"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7312
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20270701-100120.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 10 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7129
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7129"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 10:33:01"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7129"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7129
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20270701-103311.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 12 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7049
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7049"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 10:42:00"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7049"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7049
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20270701-104210.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 10 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7137
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7137"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 11:02:21"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7137"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7137
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20270701-110231.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 18 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7335
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7335"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 11:12:06"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7335"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7335
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20270701-111216.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 10 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7433
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7433"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Failed to execute the command
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7518
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7518"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 11:28:23"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7518"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Failed to execute the command
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7111
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7111"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 11:37:03"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7111"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Failed to execute the command
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7118
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7118"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 11:46:02"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7118"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7118
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20270701-114612.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 11 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7126
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7126"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 12:18:17"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7126"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7126
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20270701-121827.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 13 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7141
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7141"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 12:56:29"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7141"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Failed to execute the command
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7267
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7267"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2027-07-01 13:04:01"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7267"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7267
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20270701-130411.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 22 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A024 ***************



Verifying that core traces are created
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Input Stream : Executing:rm -rf /export/logfiles/senovision/coretraces/*
Std Input Stream : Executing:/sbin/pidof ApplicationManagerProg
Std Input Stream : Executing:kill -ABRT 7196
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7196"
Std Input Stream : Executing:date '+%Y-%m-%d %H:%M:%S'
Std Input Stream : 
Std Input Stream : Waiting for 10 seconds
Std Input Stream : 
Std Input Stream : Running Core Cleanup
Std Input Stream : Executing:systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : Checking coretraces in /export/logfiles/senovision/coretraces/
Std Input Stream : Executing:find /export/logfiles/senovision/coretraces/ -name "core_traces*.txt" -newermt "2025-07-01 13:45:48"
Std Input Stream : 
Std Input Stream : Checking core files in /export/logfiles/senovision/
Std Input Stream : Executing:find /export/logfiles/senovision/ -name "core.*.7196"
Std Input Stream : Executing:su - sdc -c "/export/home/<USER>/senovision/scripts/restart.mammo"
Std Input Stream :  retVal : 0
Std Input Stream : Exit with status: 0
Std Input Stream : Executing:ps -aef | grep 'launchCtrlAltDel.py' | grep -v grep >/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputWaitTimeSecs = 10
Std Input Stream : InputCheckProcessName = ApplicationManagerProg
Std Input Stream : InputRunCoreCleanup = systemd_MammoOSLayer S97AdsLogCleaner restart >/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream : Process ID : 7196
Std Input Stream : New core traces files created : /export/logfiles/senovision/coretraces/core_traces_20250701-134558.txt
Std Input Stream : Core files removed from /export/logfiles/senovision/
Std Input Stream : 
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_LoggingArchivingAndCleaning_A009 ***************
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log

Executing : bash -c find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
Time taken to execute script 22 ms

Thread count in core traces file : 48
Executing command : /export/home/<USER>/senovision/scripts/restart.mammo
Std Error Stream : [1]  + Done                          /export/home/<USER>/senovision/scripts/sendSystemStopEvent.sh >> $SDCHOME/senovision/logfiles/startstopmammo.log
Waiting for 60 seconds
Restarted application successfully
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputVerifyCoreTraces = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/LoggingArchivingAndCleaning/LoggingArchivingAndCleaningTestExe OsInfra_LoggingArchivingAndCleaning_A009
InputVerifyThreadCount = find /export/home/<USER>/senovision/logfiles/coretraces/ -name "core_traces_*" | xargs -I% grep -P "^Thread [0-9]+" % | cut -f1 -d"(" | sort | uniq | wc -l
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Core traces created
Core traces of multiple threads present
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A024 ***************

Test Result : SUCCESS
