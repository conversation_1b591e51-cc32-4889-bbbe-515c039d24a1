USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:878 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDate<PERSON><PERSON><PERSON>() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 406 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:205 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Caught exception while executing suifDateChange() Required SNA Link is  not Available:
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Caught exception while exiting service desktop :Unable to find element: tagname input attribute src value /modality-csd/images/exit_rect.gif
Attempting to close browser
Time taken to close browser: 448 ms
Failed to exit from service desktop
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:213 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 415 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:220 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 408 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:304 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 423 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:212 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 409 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:213 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 405 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:230 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 514 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:744 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 406 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:200 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 406 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:608 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 512 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Caught exception while loading requested URL : Process unexpectedly closed with status 1
Build info: version: '3.141.59', revision: 'e82be7d358', time: '2018-11-14T08:25:53'
System info: host: 'Pristina', ip: '127.0.0.1', os.name: 'Linux', os.arch: 'amd64', os.version: '6.4.0-150600.23.50-default', java.version: '1.8.0_452'
Driver info: driver.version: FirefoxDriver
remote stacktrace: 
Couldn't load SUIF page properly in OSInfraIntegrationA019()
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:303 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 405 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:216 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 407 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:195 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 408 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A013 ***************

Failed to read log configuration value from SuifTests.xml

Loading URL : http://localhost/modality-csd/serviceDesktop/index_local.htm
Attempting to open browser with URL http://localhost/modality-csd/serviceDesktop/index_local.htm
Time taken to load URL http://localhost/modality-csd/serviceDesktop/index_local.htm:821 ms

Page loaded successfully : http://localhost/modality-csd/serviceDesktop/index_local.htm
Opened SUIF Page successfully: 
Clicking on SUIF->Configuration
Clicking on SYSTEM ->Date-Time Config
Current Date
Caught exception while executing suifDateChange() Unparseable date: ""
Test case OsInfra_ITP_A013 failed as suifDateChange() failed to update the date 
Switching to frame leftbase
Clicking on Exit from Service desktop
Service desktop closed
Attempting to close browser
Time taken to close browser: 513 ms
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputnumberOfDaysToAdd = 2
InputnumberOfDaysToSubtract = -2
InputToFindSHMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SHMFILE*' |wc -l"
InputToFindSEMFiles = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/TestCases/shared/scripts/executeCommandInRemote.sh -u root -p Install@123 -c "find /tmp/ -name 'SEMFILE*' |wc -l"
InputToExecuteTmpwatch = bash,-c,/etc/cron.daily/tmpwatch
InputToGetNumberOfSEMFilesExpected = 5
InputToGetNumberOfSHMFilesExpected = 5
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 
 Warning:Update the actual output Properly
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = FAILURE

*************** Finished Executing the Test Case - OsInfra_ITP_A013 ***************

Test Result : FAILURE
