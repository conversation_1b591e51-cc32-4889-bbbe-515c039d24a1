USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 14 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 9 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 12 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 12 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 21 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 11 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 15 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 16 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 15 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 15 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 18 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 10 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 13 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 13 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
USER=sdc
JAVA_HOME=/usr/java/latest
/export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/SquishTestCaseSuite/config/squishCommonConfig.properties read successfully

*************** Started Executing the Test Case - OsInfra_ITP_A021 ***************


Restarting syslog server
Executing command : bash -c /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
Std Input Stream : 
Std Input Stream : *************** Started Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************
Std Input Stream : Executing:systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : 
Std Input Stream : -------------------------------- INPUT CONFIG FILE ----------------------------------------------
Std Input Stream : InputRestartSyslogServer = systemd_MammoOSLayer rsyslog restart>/dev/null;echo $?
Std Input Stream : ---------------------------------- ACTUAL OUTPUT ------------------------------------------------
Std Input Stream : ActualOuptut = 
Std Input Stream :  Warning:Update the actual output Properly
Std Input Stream : ---------------------------------- TEST RESULT --------------------------------------------------
Std Input Stream : TestCase PASS/FAIL = PASS
Std Input Stream : 
Std Input Stream : *************** Finished Executing the Test Case - OsInfra_RootCommandExecutor_A010 ***************


Running command : ':>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log'

Executing : bash -c :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
Time taken to execute script 15 ms

Expected output : 'bbbbb'

Actual output : 'bbbbb'

Expected output found
-------------------------------- INPUT CONFIG FILE ----------------------------------------------
InputRestartSyslogServer = /export/home1/axis_data/SwPltAutomatedTest/OSInfraTest/TestCaseExecutables/CommonUtils/RootCommandExecutor/RootCommandExecutorExe OsInfra_RootCommandExecutor_A010
InputRunCommand = :>/export/home/<USER>/senovision/logfiles/PoseidonEvents.log;logger -t posevents bbbbb;tac /export/home/<USER>/senovision/logfiles/PoseidonEvents.log
ExpectedOutput = bbbbb
---------------------------------- ACTUAL OUTPUT ------------------------------------------------
ActualOutput = 

Syslog server is restarted
Expected log found
---------------------------------- TEST RESULT --------------------------------------------------
TestCase PASS/FAIL = PASS

*************** Finished Executing the Test Case - OsInfra_ITP_A021 ***************

Test Result : SUCCESS
